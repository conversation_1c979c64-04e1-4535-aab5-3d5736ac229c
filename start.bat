@echo off
echo ========================================
echo Confluence MCP Server 启动脚本
echo ========================================

echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装Java 17或更高版本
    pause
    exit /b 1
)

echo.
echo 检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Maven环境，请确保已安装Maven 3.6或更高版本
    pause
    exit /b 1
)

echo.
echo 编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)

echo.
echo 启动Confluence MCP服务器...
echo 服务器将在以下端点启动:
echo   - HTTP服务: http://localhost:8080
echo   - WebSocket MCP端点: ws://localhost:8080/mcp
echo.
echo 请确保已在 application.yml 中正确配置Confluence服务器信息
echo.
echo 按 Ctrl+C 停止服务器
echo.

mvn spring-boot:run

pause
