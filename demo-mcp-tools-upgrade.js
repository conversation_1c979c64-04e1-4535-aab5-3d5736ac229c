/**
 * MCP工具列表升级演示脚本
 * 
 * 此脚本演示了升级后的MCP工具列表功能，包括：
 * 1. 分页支持
 * 2. 输出模式验证
 * 3. 工具注解
 * 4. 工具列表变更通知
 */

// 模拟MCP客户端
class McpClient {
    constructor(serverUrl) {
        this.serverUrl = serverUrl;
        this.requestId = 1;
    }

    // 发送JSON-RPC请求
    async sendRequest(method, params = {}) {
        const request = {
            jsonrpc: "2.0",
            id: this.requestId++,
            method: method,
            params: params
        };

        console.log(`📤 发送请求: ${method}`);
        console.log(JSON.stringify(request, null, 2));

        // 这里应该是实际的HTTP请求，现在只是模拟响应
        return this.simulateResponse(method, params);
    }

    // 模拟服务器响应
    simulateResponse(method, params) {
        switch (method) {
            case "initialize":
                return {
                    jsonrpc: "2.0",
                    id: this.requestId - 1,
                    result: {
                        protocolVersion: "2025-06-18",
                        serverInfo: {
                            name: "Confluence MCP Server",
                            version: "1.0.0"
                        },
                        capabilities: {
                            tools: {
                                listChanged: true
                            }
                        }
                    }
                };

            case "tools/list":
                return this.simulateToolsList(params.cursor);

            default:
                return {
                    jsonrpc: "2.0",
                    id: this.requestId - 1,
                    error: {
                        code: -32601,
                        message: "Method not found"
                    }
                };
        }
    }

    // 模拟工具列表响应
    simulateToolsList(cursor) {
        const allTools = [
            {
                name: "search_pages",
                title: "🔍 搜索页面",
                description: "在Confluence中搜索页面",
                inputSchema: {
                    type: "object",
                    properties: {
                        query: { type: "string", description: "搜索关键词" },
                        spaceKey: { type: "string", description: "空间键（可选）" },
                        limit: { type: "integer", description: "返回结果数量限制", default: 10 }
                    },
                    required: ["query"]
                },
                outputSchema: {
                    type: "object",
                    properties: {
                        content: {
                            type: "array",
                            items: {
                                type: "object",
                                properties: {
                                    type: { type: "string" },
                                    text: { type: "string" }
                                }
                            }
                        },
                        isError: { type: "boolean" }
                    },
                    required: ["content", "isError"]
                },
                annotations: {
                    audience: ["user", "assistant"],
                    priority: 0.8,
                    category: "search"
                }
            },
            {
                name: "get_page_content",
                title: "📄 获取页面内容",
                description: "获取指定页面的详细内容",
                inputSchema: {
                    type: "object",
                    properties: {
                        pageId: { type: "string", description: "页面ID" }
                    },
                    required: ["pageId"]
                },
                outputSchema: {
                    type: "object",
                    properties: {
                        content: {
                            type: "array",
                            items: {
                                type: "object",
                                properties: {
                                    type: { type: "string" },
                                    text: { type: "string" }
                                }
                            }
                        },
                        isError: { type: "boolean" }
                    },
                    required: ["content", "isError"]
                },
                annotations: {
                    audience: ["user", "assistant"],
                    priority: 0.9,
                    category: "content"
                }
            },
            {
                name: "test_connection",
                title: "🧪 测试连接",
                description: "测试MCP服务器连接状态",
                inputSchema: {
                    type: "object",
                    properties: {}
                },
                outputSchema: {
                    type: "object",
                    properties: {
                        content: {
                            type: "array",
                            items: {
                                type: "object",
                                properties: {
                                    type: { type: "string" },
                                    text: { type: "string" }
                                }
                            }
                        },
                        isError: { type: "boolean" }
                    },
                    required: ["content", "isError"]
                },
                annotations: {
                    audience: ["user", "assistant"],
                    priority: 1.0,
                    category: "testing"
                }
            }
        ];

        const pageSize = 2; // 演示分页
        const startIndex = cursor ? parseInt(cursor) : 0;
        const endIndex = Math.min(startIndex + pageSize, allTools.length);
        const pageTools = allTools.slice(startIndex, endIndex);

        const result = {
            jsonrpc: "2.0",
            id: this.requestId - 1,
            result: {
                tools: pageTools
            }
        };

        // 如果还有更多工具，添加nextCursor
        if (endIndex < allTools.length) {
            result.result.nextCursor = endIndex.toString();
        }

        return result;
    }

    // 模拟工具列表变更通知
    simulateToolsListChangedNotification() {
        return {
            jsonrpc: "2.0",
            method: "notifications/tools/list_changed"
        };
    }
}

// 演示函数
async function demonstrateMcpToolsUpgrade() {
    console.log("🚀 MCP工具列表升级演示\n");

    const client = new McpClient("ws://localhost:8081/mcp");

    // 1. 初始化连接
    console.log("1️⃣ 初始化MCP连接");
    console.log("=" .repeat(50));
    const initResponse = await client.sendRequest("initialize", {
        protocolVersion: "2025-06-18",
        clientInfo: {
            name: "Demo Client",
            version: "1.0.0"
        }
    });
    console.log("📥 响应:");
    console.log(JSON.stringify(initResponse, null, 2));
    console.log("\n");

    // 2. 获取第一页工具
    console.log("2️⃣ 获取工具列表（第一页）");
    console.log("=" .repeat(50));
    const firstPageResponse = await client.sendRequest("tools/list", {});
    console.log("📥 响应:");
    console.log(JSON.stringify(firstPageResponse, null, 2));
    console.log("\n");

    // 3. 获取下一页工具
    if (firstPageResponse.result.nextCursor) {
        console.log("3️⃣ 获取工具列表（下一页）");
        console.log("=" .repeat(50));
        const nextPageResponse = await client.sendRequest("tools/list", {
            cursor: firstPageResponse.result.nextCursor
        });
        console.log("📥 响应:");
        console.log(JSON.stringify(nextPageResponse, null, 2));
        console.log("\n");
    }

    // 4. 分析工具特性
    console.log("4️⃣ 工具特性分析");
    console.log("=" .repeat(50));
    const tools = firstPageResponse.result.tools;
    
    tools.forEach(tool => {
        console.log(`🔧 工具: ${tool.name}`);
        console.log(`   标题: ${tool.title}`);
        console.log(`   描述: ${tool.description}`);
        console.log(`   优先级: ${tool.annotations.priority}`);
        console.log(`   分类: ${tool.annotations.category}`);
        console.log(`   受众: ${tool.annotations.audience.join(", ")}`);
        console.log(`   有输入模式: ✅`);
        console.log(`   有输出模式: ✅`);
        console.log(`   有注解: ✅`);
        console.log("");
    });

    // 5. 工具列表变更通知
    console.log("5️⃣ 工具列表变更通知");
    console.log("=" .repeat(50));
    const notification = client.simulateToolsListChangedNotification();
    console.log("📥 通知:");
    console.log(JSON.stringify(notification, null, 2));
    console.log("\n");

    // 6. 总结
    console.log("6️⃣ 升级总结");
    console.log("=" .repeat(50));
    console.log("✅ 分页支持 - 支持cursor参数和nextCursor响应");
    console.log("✅ 输出模式 - 每个工具都定义了详细的输出结构");
    console.log("✅ 工具注解 - 包含优先级、分类、受众等元数据");
    console.log("✅ 变更通知 - 支持工具列表变更通知");
    console.log("✅ 向后兼容 - 保持与现有客户端的兼容性");
    console.log("✅ 符合规范 - 完全符合MCP 2025-06-18规范");
}

// 运行演示
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { McpClient, demonstrateMcpToolsUpgrade };
} else {
    demonstrateMcpToolsUpgrade().catch(console.error);
}
