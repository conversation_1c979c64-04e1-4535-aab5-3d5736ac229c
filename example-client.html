<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confluence MCP Client 示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🔗 Confluence MCP Client 示例</h1>
    
    <div class="container">
        <h2>连接状态</h2>
        <div id="status" class="status disconnected">未连接</div>
        <button id="connectBtn" onclick="connect()">连接</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
    </div>

    <div class="container">
        <h2>工具调用</h2>
        
        <div class="form-group">
            <label>工具:</label>
            <select id="toolSelect">
                <option value="search_pages">搜索页面</option>
                <option value="get_page_content">获取页面内容</option>
                <option value="list_attachments">列出附件</option>
                <option value="get_page_links">获取页面链接</option>
                <option value="list_spaces">列出空间</option>
                <option value="get_comments">获取评论</option>
            </select>
        </div>

        <div class="form-group">
            <label>查询/页面ID:</label>
            <input type="text" id="queryInput" placeholder="输入搜索关键词或页面ID">
        </div>

        <div class="form-group">
            <label>空间键:</label>
            <input type="text" id="spaceKeyInput" placeholder="可选，如 DEV">
        </div>

        <button onclick="callTool()" id="callToolBtn" disabled>调用工具</button>
        <button onclick="listResources()" id="listResourcesBtn" disabled>列出资源</button>
        <button onclick="listPrompts()" id="listPromptsBtn" disabled>列出提示</button>
    </div>

    <div class="container">
        <h2>日志</h2>
        <div id="log" class="log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        let ws = null;
        let messageId = 1;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(connected) {
            const statusDiv = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const callToolBtn = document.getElementById('callToolBtn');
            const listResourcesBtn = document.getElementById('listResourcesBtn');
            const listPromptsBtn = document.getElementById('listPromptsBtn');

            if (connected) {
                statusDiv.textContent = '已连接';
                statusDiv.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                callToolBtn.disabled = false;
                listResourcesBtn.disabled = false;
                listPromptsBtn.disabled = false;
            } else {
                statusDiv.textContent = '未连接';
                statusDiv.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                callToolBtn.disabled = true;
                listResourcesBtn.disabled = true;
                listPromptsBtn.disabled = true;
            }
        }

        function connect() {
            try {
                ws = new WebSocket('ws://localhost:8080/mcp');
                
                ws.onopen = function() {
                    log('WebSocket连接已建立');
                    updateStatus(true);
                    
                    // 发送初始化消息
                    const initMessage = {
                        jsonrpc: "2.0",
                        id: messageId++,
                        method: "initialize",
                        params: {
                            protocolVersion: "2025-06-18",
                            clientInfo: {
                                name: "Confluence MCP Test Client",
                                version: "1.0.0"
                            }
                        }
                    };
                    
                    ws.send(JSON.stringify(initMessage));
                    log('发送初始化消息: ' + JSON.stringify(initMessage, null, 2));
                };
                
                ws.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    log('收到消息: ' + JSON.stringify(message, null, 2));
                };
                
                ws.onclose = function() {
                    log('WebSocket连接已关闭');
                    updateStatus(false);
                };
                
                ws.onerror = function(error) {
                    log('WebSocket错误: ' + error);
                    updateStatus(false);
                };
                
            } catch (error) {
                log('连接失败: ' + error.message);
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function callTool() {
            if (!ws) return;

            const tool = document.getElementById('toolSelect').value;
            const query = document.getElementById('queryInput').value;
            const spaceKey = document.getElementById('spaceKeyInput').value;

            let arguments = {};

            switch (tool) {
                case 'search_pages':
                    arguments.query = query || '文档';
                    if (spaceKey) arguments.spaceKey = spaceKey;
                    arguments.limit = 10;
                    break;
                case 'get_page_content':
                case 'list_attachments':
                case 'get_page_links':
                case 'get_comments':
                    arguments.pageId = query || '123456';
                    arguments.limit = 10;
                    break;
                case 'list_spaces':
                    arguments.limit = 20;
                    break;
            }

            const message = {
                jsonrpc: "2.0",
                id: messageId++,
                method: "tools/call",
                params: {
                    name: tool,
                    arguments: arguments
                }
            };

            ws.send(JSON.stringify(message));
            log('调用工具: ' + JSON.stringify(message, null, 2));
        }

        function listResources() {
            if (!ws) return;

            const message = {
                jsonrpc: "2.0",
                id: messageId++,
                method: "resources/list",
                params: {}
            };

            ws.send(JSON.stringify(message));
            log('列出资源: ' + JSON.stringify(message, null, 2));
        }

        function listPrompts() {
            if (!ws) return;

            const message = {
                jsonrpc: "2.0",
                id: messageId++,
                method: "prompts/list",
                params: {}
            };

            ws.send(JSON.stringify(message));
            log('列出提示: ' + JSON.stringify(message, null, 2));
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            updateStatus(false);
            log('Confluence MCP客户端已准备就绪');
            log('请先连接到MCP服务器，然后尝试各种功能');
        };
    </script>
</body>
</html>
