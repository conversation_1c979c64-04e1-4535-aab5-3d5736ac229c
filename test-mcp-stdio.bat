@echo off
echo ========================================
echo 测试Confluence MCP Server (STDIO模式)
echo ========================================

echo 编译项目...
./mvnw.cmd clean compile
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo.
echo 🧪 启动测试...
echo 将发送初始化消息和工具列表请求

echo.
echo 📤 发送初始化消息...
echo {"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2025-06-18","clientInfo":{"name":"Test Client","version":"1.0.0"},"capabilities":{}}} | ./mvnw.cmd exec:java -Dexec.mainClass="com.confluencemcpserver.McpStdioApplication" -q

echo.
echo 📤 发送工具列表请求...
echo {"jsonrpc":"2.0","id":2,"method":"tools/list","params":{}} | ./mvnw.cmd exec:java -Dexec.mainClass="com.confluencemcpserver.McpStdioApplication" -q

echo.
echo 📤 测试连接工具...
echo {"jsonrpc":"2.0","id":3,"method":"tools/call","params":{"name":"test_connection","arguments":{}}} | ./mvnw.cmd exec:java -Dexec.mainClass="com.confluencemcpserver.McpStdioApplication" -q

pause
