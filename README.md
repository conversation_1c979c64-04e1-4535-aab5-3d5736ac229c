# Confluence MCP Server

一个基于Model Context Protocol (MCP)的Confluence集成服务器，为AI助手提供访问本地Confluence实例的能力。

## 功能特性

### 🔍 资源 (Resources)
- **页面资源**: `confluence://page/{pageId}` - 获取页面内容
- **附件资源**: `confluence://attachment/{attachmentId}` - 获取附件信息
- **空间资源**: `confluence://space/{spaceKey}` - 获取空间信息

### 🛠️ 工具 (Tools)
- **search_pages**: 搜索页面
- **get_page_content**: 获取页面详细内容
- **list_attachments**: 列出页面附件
- **get_page_links**: 获取页面中的链接
- **list_spaces**: 列出所有空间
- **get_comments**: 获取页面评论

### 💡 提示 (Prompts)
- **analyze_page**: 分析页面内容的提示模板
- **summarize_space**: 总结空间内容的提示模板
- **compare_pages**: 比较页面的提示模板
- **extract_key_info**: 提取关键信息的提示模板

## 快速开始

### 1. 环境要求
- Java 17+
- Maven 3.6+
- 可访问的Confluence服务器

### 2. 配置

编辑 `src/main/resources/application.yml` 文件：

```yaml
confluence:
  base-url: http://your-confluence-server:8080/confluence
  username: your-username
  password: your-password-or-api-token
```

### 3. 运行

```bash
# 编译项目
mvn clean compile

# 运行服务器
mvn spring-boot:run
```

服务器将在以下端点启动：
- HTTP服务: `http://localhost:8080`
- WebSocket MCP端点: `ws://localhost:8080/mcp`

### 4. 连接MCP客户端

使用支持MCP协议的客户端连接到WebSocket端点：

```javascript
const ws = new WebSocket('ws://localhost:8080/mcp');

// 初始化连接
ws.send(JSON.stringify({
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
        protocolVersion: "2025-06-18",
        clientInfo: {
            name: "Your Client",
            version: "1.0.0"
        }
    }
}));
```

## 使用示例

### 搜索页面

```json
{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/call",
    "params": {
        "name": "search_pages",
        "arguments": {
            "query": "API文档",
            "spaceKey": "DEV",
            "limit": 10
        }
    }
}
```

### 获取页面内容

```json
{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "tools/call",
    "params": {
        "name": "get_page_content",
        "arguments": {
            "pageId": "123456"
        }
    }
}
```

### 读取资源

```json
{
    "jsonrpc": "2.0",
    "id": 4,
    "method": "resources/read",
    "params": {
        "uri": "confluence://page/123456"
    }
}
```

### 使用提示

```json
{
    "jsonrpc": "2.0",
    "id": 5,
    "method": "prompts/get",
    "params": {
        "name": "analyze_page",
        "arguments": {
            "pageId": "123456",
            "focus": "技术文档质量"
        }
    }
}
```

## 配置选项

### Confluence配置
- `confluence.base-url`: Confluence服务器地址
- `confluence.username`: 用户名
- `confluence.password`: 密码或API Token
- `confluence.connect-timeout`: 连接超时时间
- `confluence.read-timeout`: 读取超时时间
- `confluence.ssl-verification`: SSL验证开关

### MCP配置
- `mcp.server-name`: 服务器名称
- `mcp.server-version`: 服务器版本
- `mcp.websocket-path`: WebSocket路径
- `mcp.resources-enabled`: 启用资源功能
- `mcp.tools-enabled`: 启用工具功能
- `mcp.prompts-enabled`: 启用提示功能

## 安全注意事项

1. **认证**: 确保使用强密码或API Token
2. **网络**: 在生产环境中限制WebSocket连接的来源
3. **权限**: 使用具有适当权限的Confluence账户
4. **SSL**: 在生产环境中启用SSL/TLS

## 测试

### 运行单元测试

```bash
# 运行所有测试
mvn test

# 运行特定测试
mvn test -Dtest=McpServerTest
mvn test -Dtest=WebSocketTransportTest
mvn test -Dtest=HealthControllerTest
```

### 集成测试

1. **启动服务器**
```bash
mvn spring-boot:run
```

2. **使用命令行测试客户端**
```bash
mvn exec:java -Dexec.mainClass="com.confluencemcpserver.TestClient" -Dexec.classpathScope=test
```

3. **使用浏览器测试客户端**
打开 `example-client.html` 文件

4. **API健康检查**
```bash
# Windows
test-api.bat

# 或手动测试
curl http://localhost:8080/api/health
curl http://localhost:8080/api/status
curl http://localhost:8080/api/info
```

### 测试MCP功能

#### 1. 初始化连接
```json
{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
        "protocolVersion": "2025-06-18",
        "clientInfo": {"name": "Test Client", "version": "1.0.0"}
    }
}
```

#### 2. 列出工具
```json
{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/list",
    "params": {}
}
```

#### 3. 搜索页面
```json
{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "tools/call",
    "params": {
        "name": "search_pages",
        "arguments": {"query": "API文档", "limit": 5}
    }
}
```

#### 4. 列出资源
```json
{
    "jsonrpc": "2.0",
    "id": 4,
    "method": "resources/list",
    "params": {}
}
```

### 验证响应

成功的响应应该包含：
- `jsonrpc: "2.0"`
- `id`: 对应请求的ID
- `result`: 包含实际数据
- 没有 `error` 字段

错误响应会包含：
- `error.code`: 错误代码
- `error.message`: 错误描述

## 故障排除

### 常见问题

1. **连接失败**
   - 检查Confluence服务器地址是否正确
   - 验证用户名和密码
   - 确认网络连接

2. **权限错误**
   - 确保用户有访问相应空间和页面的权限
   - 检查API Token是否有效

3. **WebSocket连接问题**
   - 检查防火墙设置
   - 验证端口是否被占用

4. **测试失败**
   - 确保Confluence服务器可访问
   - 检查配置文件中的连接信息
   - 查看日志输出

### 日志调试

启用调试日志：

```yaml
logging:
  level:
    com.confluencemcpserver: DEBUG
```

## 开发

### 项目结构

```
src/main/java/com/confluencemcpserver/
├── config/                 # 配置类
├── confluence/            # Confluence集成
│   ├── model/            # 数据模型
│   └── ConfluenceClient.java
├── dto/                   # MCP数据传输对象
├── mcp/                   # MCP协议实现
│   ├── handlers/         # 请求处理器
│   ├── protocol/         # 协议定义
│   ├── transport/        # 传输层
│   └── McpServer.java
└── ConfluenceMcpServerApplication.java
```

### 扩展功能

要添加新的工具或资源类型：

1. 在相应的Handler中添加新方法
2. 更新工具或资源列表
3. 实现具体的业务逻辑

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
