#!/usr/bin/env python3
"""
Interactive MCP Server Tester
"""

import json
import subprocess
import sys
import threading
import time
from queue import Queue, Empty

class MCPTester:
    def __init__(self, server_script="minimal_mcp.py"):
        self.server_script = server_script
        self.process = None
        self.message_id = 1
        self.response_queue = Queue()
        
    def start_server(self):
        """Start the MCP server"""
        print(f"🚀 启动MCP服务器: {self.server_script}")
        self.process = subprocess.Popen(
            [sys.executable, self.server_script],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        
        # Start output reader thread
        self.output_thread = threading.Thread(target=self._read_output, daemon=True)
        self.output_thread.start()
        
        # Wait a moment for server to start
        time.sleep(1)
        print("✅ 服务器已启动")
        
    def _read_output(self):
        """Read server output in a separate thread"""
        while self.process and self.process.poll() is None:
            try:
                line = self.process.stdout.readline()
                if line:
                    line = line.strip()
                    if line.startswith('{'):
                        try:
                            response = json.loads(line)
                            self.response_queue.put(response)
                        except json.JSONDecodeError:
                            print(f"⚠️ 无法解析响应: {line}")
                    else:
                        print(f"📝 服务器日志: {line}")
            except Exception as e:
                print(f"❌ 读取输出错误: {e}")
                break
    
    def send_message(self, method, params=None):
        """Send a message to the server"""
        message = {
            "jsonrpc": "2.0",
            "id": self.message_id,
            "method": method,
            "params": params or {}
        }
        self.message_id += 1
        
        message_json = json.dumps(message) + "\n"
        print(f"📤 发送: {method}")
        
        try:
            self.process.stdin.write(message_json)
            self.process.stdin.flush()
            return self.message_id - 1
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            return None
    
    def wait_for_response(self, timeout=5):
        """Wait for a response from the server"""
        try:
            response = self.response_queue.get(timeout=timeout)
            return response
        except Empty:
            print("⏰ 响应超时")
            return None
    
    def test_initialize(self):
        """Test server initialization"""
        print("\n🔧 测试初始化...")
        msg_id = self.send_message("initialize", {
            "protocolVersion": "2025-06-18",
            "clientInfo": {
                "name": "Interactive Test Client",
                "version": "1.0.0"
            },
            "capabilities": {}
        })
        
        if msg_id:
            response = self.wait_for_response()
            if response and "result" in response:
                print("✅ 初始化成功")
                server_info = response["result"].get("serverInfo", {})
                print(f"   服务器: {server_info.get('name', 'Unknown')}")
                print(f"   版本: {server_info.get('version', 'Unknown')}")
                return True
            else:
                print(f"❌ 初始化失败: {response}")
                return False
        return False
    
    def test_list_tools(self):
        """Test listing tools"""
        print("\n🛠️ 测试工具列表...")
        msg_id = self.send_message("tools/list")
        
        if msg_id:
            response = self.wait_for_response()
            if response and "result" in response:
                tools = response["result"].get("tools", [])
                print(f"✅ 找到 {len(tools)} 个工具:")
                for tool in tools:
                    print(f"   - {tool['name']}: {tool['description']}")
                return tools
            else:
                print(f"❌ 获取工具列表失败: {response}")
                return []
        return []
    
    def test_tool_call(self, tool_name, arguments=None):
        """Test calling a specific tool"""
        print(f"\n🔧 测试工具调用: {tool_name}")
        msg_id = self.send_message("tools/call", {
            "name": tool_name,
            "arguments": arguments or {}
        })
        
        if msg_id:
            response = self.wait_for_response(timeout=10)
            if response and "result" in response:
                print("✅ 工具调用成功")
                content = response["result"].get("content", [])
                if content and len(content) > 0:
                    text = content[0].get("text", "")
                    print(f"   结果: {text[:200]}...")
                return True
            else:
                print(f"❌ 工具调用失败: {response}")
                return False
        return False
    
    def interactive_mode(self):
        """Interactive testing mode"""
        print("\n🎮 进入交互模式")
        print("可用命令:")
        print("  1 - 测试hello_world工具")
        print("  2 - 测试search_pages工具")
        print("  3 - 测试list_spaces工具")
        print("  q - 退出")
        
        while True:
            try:
                choice = input("\n请选择命令: ").strip()
                
                if choice == "q":
                    break
                elif choice == "1":
                    self.test_tool_call("hello_world")
                elif choice == "2":
                    query = input("请输入搜索关键词 (默认: API): ").strip() or "API"
                    self.test_tool_call("search_pages", {"query": query, "limit": 3})
                elif choice == "3":
                    self.test_tool_call("list_spaces", {"limit": 5})
                else:
                    print("❌ 无效命令")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
    
    def cleanup(self):
        """Clean up resources"""
        if self.process:
            print("\n🧹 清理资源...")
            self.process.terminate()
            self.process.wait()
            print("✅ 清理完成")

def main():
    print("========================================")
    print("Confluence MCP Server 交互式测试")
    print("========================================")
    
    # Choose server to test
    print("\n选择要测试的服务器:")
    print("1. minimal_mcp.py (最简版本)")
    print("2. confluence_mcp_server.py (完整版本)")
    
    choice = input("请选择 (1/2, 默认1): ").strip() or "1"
    
    if choice == "2":
        server_script = "confluence_mcp_server.py"
    else:
        server_script = "minimal_mcp.py"
    
    tester = MCPTester(server_script)
    
    try:
        tester.start_server()
        
        # Run basic tests
        if tester.test_initialize():
            tools = tester.test_list_tools()
            
            if tools:
                # Test first tool
                first_tool = tools[0]["name"]
                tester.test_tool_call(first_tool)
                
                # Enter interactive mode
                tester.interactive_mode()
            else:
                print("❌ 没有可用工具，无法继续测试")
        else:
            print("❌ 初始化失败，无法继续测试")
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    finally:
        tester.cleanup()

if __name__ == "__main__":
    main()
