# Confluence MCP Server 配置示例
# 复制此文件为 src/main/resources/application.yml 并修改相应配置

server:
  port: 8080

# Confluence 服务器配置
confluence:
  # 替换为你的Confluence服务器地址
  base-url: http://localhost:8080/confluence
  # 替换为你的用户名
  username: admin
  # 替换为你的密码或API Token（推荐使用API Token）
  password: admin
  # 连接超时时间（毫秒）
  connect-timeout: 30000
  # 读取超时时间（毫秒）
  read-timeout: 60000
  # 是否启用SSL验证（生产环境建议启用）
  ssl-verification: true

# MCP 服务器配置
mcp:
  server-name: "Confluence MCP Server"
  server-version: "1.0.0"
  websocket-port: 8081
  websocket-path: "/mcp"
  # 功能开关
  resources-enabled: true
  tools-enabled: true
  prompts-enabled: true
  resource-subscription-enabled: true
  resource-list-changed-enabled: true

# 日志配置
logging:
  level:
    # 应用日志级别
    com.confluencemcpserver: INFO
    # WebSocket日志级别（调试时可设为DEBUG）
    org.springframework.web.socket: INFO
    # HTTP客户端日志级别
    org.springframework.web.reactive.function.client: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/confluence-mcp-server.log

# Spring Boot 配置
spring:
  application:
    name: confluence-mcp-server
  main:
    web-application-type: servlet

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev

logging:
  level:
    com.confluencemcpserver: DEBUG
    org.springframework.web.socket: DEBUG

confluence:
  ssl-verification: false

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod

logging:
  level:
    com.confluencemcpserver: WARN
    org.springframework.web.socket: WARN

confluence:
  ssl-verification: true
  connect-timeout: 10000
  read-timeout: 30000
