@echo off
echo ========================================
echo Confluence MCP Server (Upgraded)
echo Version: 1.0.0-UPGRADED
echo ========================================
echo.

REM 设置环境变量
set CONFLUENCE_BASE_URL=https://your-confluence-domain.atlassian.net
set CONFLUENCE_USERNAME=<EMAIL>
set CONFLUENCE_API_TOKEN=your-api-token
set SPRING_PROFILES_ACTIVE=production

REM 设置MCP特性开关
set MCP_TOOLS_LIST_CHANGED_ENABLED=true
set MCP_RESOURCES_ENABLED=true
set MCP_PROMPTS_ENABLED=true

echo 配置信息:
echo - Confluence URL: %CONFLUENCE_BASE_URL%
echo - Username: %CONFLUENCE_USERNAME%
echo - API Token: [已设置]
echo - Profile: %SPRING_PROFILES_ACTIVE%
echo.
echo MCP功能:
echo - 工具列表变更通知: %MCP_TOOLS_LIST_CHANGED_ENABLED%
echo - 资源功能: %MCP_RESOURCES_ENABLED%
echo - 提示功能: %MCP_PROMPTS_ENABLED%
echo.

echo 启动MCP服务器...
java -jar target\confluenceMcpServer-1.0.0-UPGRADED.jar

pause
