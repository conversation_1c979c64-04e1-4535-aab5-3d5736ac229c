@echo off
echo ========================================
echo MCP服务器诊断工具
echo ========================================

echo 1. 检查端口占用情况...
netstat -an | findstr :8080
echo.

echo 2. 检查Java进程...
tasklist | findstr java
echo.

echo 3. 尝试访问健康检查端点...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8080/api/health' -UseBasicParsing; Write-Host '✅ 健康检查成功:'; $response.Content } catch { Write-Host '❌ 健康检查失败:' $_.Exception.Message }"
echo.

echo 4. 尝试访问状态端点...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8080/api/status' -UseBasicParsing; Write-Host '✅ 状态检查成功:'; $response.Content } catch { Write-Host '❌ 状态检查失败:' $_.Exception.Message }"
echo.

echo 5. 检查WebSocket端点...
echo 请打开浏览器访问: file:///%CD%/test-mcp-connection.html
echo 或者手动测试WebSocket连接: ws://localhost:8080/mcp
echo.

echo 6. Augment配置建议...
echo 在Augment中使用以下配置:
echo.
echo 方法1 - 直接配置:
echo   名称: Confluence MCP Server
echo   命令: ws://localhost:8080/mcp
echo   类型: WebSocket
echo.
echo 方法2 - 配置文件:
echo   使用文件: mcp-config-for-augment.json
echo.

echo 7. 常见问题排查...
echo.
echo 如果Augment无法连接:
echo   - 确保MCP服务器正在运行 (mvn spring-boot:run)
echo   - 检查防火墙设置
echo   - 验证端口8080未被其他程序占用
echo   - 确保Augment配置中的URL正确
echo.
echo 如果工具列表为空:
echo   - 检查MCP服务器日志
echo   - 确保Confluence配置正确
echo   - 验证初始化握手成功
echo.

pause
