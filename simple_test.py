#!/usr/bin/env python3
"""
Simple test for MCP server
"""

import json
import subprocess
import sys

def test_mcp_server():
    """Test the MCP server with a simple initialize message"""
    print("🧪 测试MCP服务器...")
    
    # Start the server
    process = subprocess.Popen(
        [sys.executable, "confluence_mcp_server.py"],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    # Send initialize message
    init_message = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2025-06-18",
            "clientInfo": {
                "name": "Test Client",
                "version": "1.0.0"
            }
        }
    }
    
    try:
        # Send message
        message_json = json.dumps(init_message) + "\n"
        process.stdin.write(message_json)
        process.stdin.flush()
        
        # Read response (with timeout)
        import select
        import time
        
        start_time = time.time()
        while time.time() - start_time < 5:  # 5 second timeout
            if process.stdout.readable():
                response_line = process.stdout.readline()
                if response_line:
                    try:
                        response = json.loads(response_line)
                        print("✅ 收到响应:")
                        print(json.dumps(response, indent=2, ensure_ascii=False))
                        
                        if "result" in response:
                            print("✅ MCP服务器工作正常!")
                            return True
                        else:
                            print("❌ 响应中没有result字段")
                            return False
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析错误: {e}")
                        print(f"原始响应: {response_line}")
                        return False
            time.sleep(0.1)
        
        print("❌ 响应超时")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        process.terminate()
        process.wait()

if __name__ == "__main__":
    test_mcp_server()
