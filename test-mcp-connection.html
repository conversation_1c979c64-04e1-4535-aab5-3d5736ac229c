<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP连接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .log { background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        button { padding: 8px 16px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:disabled { background-color: #6c757d; }
    </style>
</head>
<body>
    <h1>🔗 MCP连接测试工具</h1>
    
    <div id="status" class="status info">准备测试连接...</div>
    
    <button onclick="testConnection()">测试连接</button>
    <button onclick="testInitialize()">测试初始化</button>
    <button onclick="testListTools()">测试列出工具</button>
    <button onclick="clearLog()">清空日志</button>
    
    <h2>📋 测试日志</h2>
    <div id="log" class="log"></div>

    <script>
        let ws = null;
        let messageId = 1;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logDiv.innerHTML += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function testConnection() {
            log('开始测试WebSocket连接...');
            updateStatus('正在连接...', 'info');

            try {
                ws = new WebSocket('ws://localhost:8081/mcp');
                
                ws.onopen = function() {
                    log('WebSocket连接成功建立', 'success');
                    updateStatus('连接成功！', 'success');
                };
                
                ws.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        log('收到消息: ' + JSON.stringify(message, null, 2));
                    } catch (e) {
                        log('收到非JSON消息: ' + event.data);
                    }
                };
                
                ws.onclose = function(event) {
                    log(`WebSocket连接关闭: 代码=${event.code}, 原因=${event.reason}`, 'error');
                    updateStatus('连接已关闭', 'error');
                };
                
                ws.onerror = function(error) {
                    log('WebSocket错误: ' + error, 'error');
                    updateStatus('连接错误', 'error');
                };
                
                // 5秒后检查连接状态
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        log('连接超时', 'error');
                        updateStatus('连接超时', 'error');
                        ws.close();
                    }
                }, 5000);
                
            } catch (error) {
                log('连接失败: ' + error.message, 'error');
                updateStatus('连接失败', 'error');
            }
        }

        function testInitialize() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('请先建立WebSocket连接', 'error');
                return;
            }

            const initMessage = {
                jsonrpc: "2.0",
                id: messageId++,
                method: "initialize",
                params: {
                    protocolVersion: "2025-06-18",
                    clientInfo: {
                        name: "MCP Connection Test",
                        version: "1.0.0"
                    }
                }
            };

            log('发送初始化消息...');
            ws.send(JSON.stringify(initMessage));
        }

        function testListTools() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('请先建立WebSocket连接', 'error');
                return;
            }

            const toolsMessage = {
                jsonrpc: "2.0",
                id: messageId++,
                method: "tools/list",
                params: {}
            };

            log('请求工具列表...');
            ws.send(JSON.stringify(toolsMessage));
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            log('MCP连接测试工具已加载');
            log('点击"测试连接"按钮开始测试');
        };
    </script>
</body>
</html>
