# Confluence MCP Server - 最终配置指南

## 🎉 打包完成

✅ **JAR文件**: `target/confluenceMcpServer-1.0.0-UPGRADED.jar`  
✅ **文件大小**: ~30MB  
✅ **启动测试**: 通过  
✅ **版本**: 1.0.0-UPGRADED (符合MCP 2025-06-18规范)

## 🚀 快速开始

### 1. 基本启动命令

```bash
java -jar target/confluenceMcpServer-1.0.0-UPGRADED.jar
```

### 2. 带环境变量启动

```bash
# Windows
set CONFLUENCE_BASE_URL=https://your-domain.atlassian.net
set CONFLUENCE_USERNAME=<EMAIL>
set CONFLUENCE_API_TOKEN=your-api-token
java -jar target/confluenceMcpServer-1.0.0-UPGRADED.jar

# Linux/macOS
export CONFLUENCE_BASE_URL=https://your-domain.atlassian.net
export CONFLUENCE_USERNAME=<EMAIL>
export CONFLUENCE_API_TOKEN=your-api-token
java -jar target/confluenceMcpServer-1.0.0-UPGRADED.jar
```

## 📋 MCP客户端配置

### Claude Desktop 配置

**配置文件位置**:
- Windows: `%APPDATA%\Claude\claude_desktop_config.json`
- macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`

**配置内容**:
```json
{
  "mcpServers": {
    "confluence-mcp-upgraded": {
      "command": "java",
      "args": [
        "-jar",
        "C:/Users/<USER>/Desktop/idea/confluenceMcpServer/target/confluenceMcpServer-1.0.0-UPGRADED.jar"
      ],
      "env": {
        "CONFLUENCE_BASE_URL": "https://your-confluence-domain.atlassian.net",
        "CONFLUENCE_USERNAME": "<EMAIL>",
        "CONFLUENCE_API_TOKEN": "your-api-token",
        "SPRING_PROFILES_ACTIVE": "production"
      }
    }
  }
}
```

### Augment 配置

**配置文件**: `augment_mcp_config_upgraded.json`

```json
{
  "mcpServers": {
    "confluence-mcp-upgraded": {
      "command": "java",
      "args": [
        "-jar",
        "C:/Users/<USER>/Desktop/idea/confluenceMcpServer/target/confluenceMcpServer-1.0.0-UPGRADED.jar"
      ],
      "env": {
        "CONFLUENCE_BASE_URL": "https://your-confluence-domain.atlassian.net",
        "CONFLUENCE_USERNAME": "<EMAIL>", 
        "CONFLUENCE_API_TOKEN": "your-api-token",
        "SPRING_PROFILES_ACTIVE": "production",
        "MCP_TOOLS_LIST_CHANGED_ENABLED": "true",
        "MCP_RESOURCES_ENABLED": "true",
        "MCP_PROMPTS_ENABLED": "true"
      }
    }
  }
}
```

## 🔧 环境变量说明

### 必需变量
| 变量名 | 说明 | 示例 |
|--------|------|------|
| `CONFLUENCE_BASE_URL` | Confluence实例URL | `https://company.atlassian.net` |
| `CONFLUENCE_USERNAME` | 用户邮箱 | `<EMAIL>` |
| `CONFLUENCE_API_TOKEN` | API令牌 | `ATATT3xFfGF0...` |

### 可选变量
| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `SPRING_PROFILES_ACTIVE` | `default` | Spring配置文件 |
| `MCP_TOOLS_LIST_CHANGED_ENABLED` | `true` | 工具列表变更通知 |
| `MCP_RESOURCES_ENABLED` | `true` | 资源功能开关 |
| `MCP_PROMPTS_ENABLED` | `true` | 提示功能开关 |

## 🛠️ 新功能特性

### 1. 分页支持
- 支持 `cursor` 参数
- 返回 `nextCursor` 字段
- 默认页面大小: 10个工具

### 2. 输出模式
- 每个工具都有详细的 `outputSchema`
- 支持结构化结果验证
- 符合JSON Schema规范

### 3. 工具注解
- `audience`: 目标受众 (user, assistant)
- `priority`: 优先级 (0.0-1.0)
- `category`: 分类标签

### 4. 工具列表变更通知
- 支持 `notifications/tools/list_changed`
- 可配置开关控制

## 📊 可用工具列表

| 工具名称 | 优先级 | 分类 | 功能描述 |
|---------|--------|------|----------|
| `test_connection` | 1.0 | testing | 测试MCP服务器连接 |
| `get_page_content` | 0.9 | content | 获取页面详细内容 |
| `search_pages` | 0.8 | search | 搜索Confluence页面 |
| `list_spaces` | 0.7 | spaces | 列出所有空间 |
| `list_attachments` | 0.6 | attachments | 列出页面附件 |
| `get_page_links` | 0.5 | links | 提取页面链接 |
| `get_comments` | 0.4 | comments | 获取页面评论 |

## 🧪 测试验证

### 1. 基本连接测试
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "test_connection",
    "arguments": {}
  }
}
```

### 2. 工具列表测试
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "method": "tools/list",
  "params": {}
}
```

### 3. 分页测试
```json
{
  "jsonrpc": "2.0",
  "id": 3,
  "method": "tools/list",
  "params": {
    "cursor": "0"
  }
}
```

## 🔍 故障排除

### 常见问题

1. **启动失败**
   - 检查Java版本 (需要JDK 17+)
   - 确认JAR文件完整性

2. **连接Confluence失败**
   - 验证URL格式正确
   - 检查API Token有效性
   - 确认网络连接

3. **权限问题**
   - 确认用户有Confluence访问权限
   - 检查API Token权限范围

### 日志级别调整

```bash
# 详细日志
export LOGGING_LEVEL_COM_CONFLUENCEMCPSERVER=DEBUG

# 生产环境
export LOGGING_LEVEL_ROOT=WARN
export LOGGING_LEVEL_COM_CONFLUENCEMCPSERVER=INFO
```

## 📁 文件清单

### 核心文件
- `confluenceMcpServer-1.0.0-UPGRADED.jar` - 主程序
- `claude_desktop_config_upgraded.json` - Claude Desktop配置
- `augment_mcp_config_upgraded.json` - Augment配置

### 启动脚本
- `start-mcp-upgraded.bat` - Windows启动脚本
- `test-mcp-upgraded.bat` - 测试脚本

### 文档
- `DEPLOYMENT_GUIDE.md` - 部署指南
- `MCP_TOOLS_UPGRADE.md` - 升级详情
- `UPGRADE_SUMMARY.md` - 升级总结

## 🎯 下一步操作

1. **配置Confluence连接信息**
   - 获取API Token
   - 设置环境变量

2. **选择MCP客户端**
   - Claude Desktop
   - Augment
   - 其他支持MCP的客户端

3. **部署和测试**
   - 启动服务器
   - 配置客户端
   - 验证功能

4. **生产环境优化**
   - 调整JVM参数
   - 配置日志级别
   - 监控性能

---

**🎉 恭喜！Confluence MCP Server 已成功升级并打包完成！**

现在您可以享受符合最新MCP规范的强大功能，包括分页支持、输出模式验证、工具注解和变更通知等特性。
