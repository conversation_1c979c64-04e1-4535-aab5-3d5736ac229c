"""
Configuration for Confluence MCP Server
"""

import os

# Confluence server configuration
CONFLUENCE_BASE_URL = os.getenv("CONFLUENCE_BASE_URL", "http://localhost:8080/confluence")
CONFLUENCE_USERNAME = os.getenv("CONFLUENCE_USERNAME", "admin")
CONFLUENCE_PASSWORD = os.getenv("CONFLUENCE_PASSWORD", "admin")

# Request timeout in seconds
REQUEST_TIMEOUT = float(os.getenv("REQUEST_TIMEOUT", "30.0"))

# Logging configuration
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
