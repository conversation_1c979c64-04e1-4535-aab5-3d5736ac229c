@echo off
echo ========================================
echo Confluence MCP Server 测试脚本
echo ========================================

echo 1. 运行单元测试...
mvn test -Dtest=McpServerTest
if %errorlevel% neq 0 (
    echo ❌ 单元测试失败
    pause
    exit /b 1
)

echo.
echo 2. 运行WebSocket传输测试...
mvn test -Dtest=WebSocketTransportTest
if %errorlevel% neq 0 (
    echo ❌ WebSocket传输测试失败
    pause
    exit /b 1
)

echo.
echo 3. 启动服务器进行集成测试...
echo 请在另一个终端窗口运行以下命令启动服务器:
echo mvn spring-boot:run
echo.
echo 然后运行测试客户端:
echo mvn exec:java -Dexec.mainClass="com.confluencemcpserver.TestClient" -Dexec.classpathScope=test
echo.
echo 或者打开浏览器访问: file:///%CD%/example-client.html
echo.

pause
