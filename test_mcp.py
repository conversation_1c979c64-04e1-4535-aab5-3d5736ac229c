#!/usr/bin/env python3
"""
Test script for Confluence MCP Server
"""

import asyncio
import json
import subprocess
import sys
from typing import Dict, Any

class MCPTester:
    def __init__(self):
        self.process = None
        self.message_id = 1
    
    async def start_server(self):
        """Start the MCP server process"""
        self.process = await asyncio.create_subprocess_exec(
            sys.executable, "confluence_mcp_server.py",
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        print("✅ MCP服务器已启动")
    
    async def send_message(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Send a JSON-RPC message to the server"""
        message = {
            "jsonrpc": "2.0",
            "id": self.message_id,
            "method": method,
            "params": params or {}
        }
        self.message_id += 1
        
        message_json = json.dumps(message) + "\n"
        self.process.stdin.write(message_json.encode())
        await self.process.stdin.drain()
        
        # Read response
        response_line = await self.process.stdout.readline()
        response = json.loads(response_line.decode())
        
        return response
    
    async def test_initialize(self):
        """Test server initialization"""
        print("\n🔧 测试初始化...")
        response = await self.send_message("initialize", {
            "protocolVersion": "2025-06-18",
            "clientInfo": {
                "name": "MCP Test Client",
                "version": "1.0.0"
            }
        })
        
        if "result" in response:
            print("✅ 初始化成功")
            print(f"   服务器: {response['result']['serverInfo']['name']}")
            print(f"   版本: {response['result']['serverInfo']['version']}")
            return True
        else:
            print(f"❌ 初始化失败: {response.get('error', 'Unknown error')}")
            return False
    
    async def test_list_tools(self):
        """Test listing available tools"""
        print("\n🛠️ 测试工具列表...")
        response = await self.send_message("tools/list")
        
        if "result" in response and "tools" in response["result"]:
            tools = response["result"]["tools"]
            print(f"✅ 找到 {len(tools)} 个工具:")
            for tool in tools:
                print(f"   - {tool['name']}: {tool['description']}")
            return True
        else:
            print(f"❌ 获取工具列表失败: {response.get('error', 'Unknown error')}")
            return False
    
    async def test_search_pages(self):
        """Test search pages tool"""
        print("\n🔍 测试搜索页面...")
        response = await self.send_message("tools/call", {
            "name": "search_pages",
            "arguments": {
                "query": "API",
                "limit": 3
            }
        })
        
        if "result" in response:
            print("✅ 搜索页面成功")
            content = response["result"]["content"][0]["text"]
            print(f"   结果预览: {content[:200]}...")
            return True
        else:
            print(f"❌ 搜索页面失败: {response.get('error', 'Unknown error')}")
            return False
    
    async def test_list_spaces(self):
        """Test list spaces tool"""
        print("\n📁 测试列出空间...")
        response = await self.send_message("tools/call", {
            "name": "list_spaces",
            "arguments": {
                "limit": 5
            }
        })
        
        if "result" in response:
            print("✅ 列出空间成功")
            content = response["result"]["content"][0]["text"]
            print(f"   结果预览: {content[:200]}...")
            return True
        else:
            print(f"❌ 列出空间失败: {response.get('error', 'Unknown error')}")
            return False
    
    async def cleanup(self):
        """Clean up the server process"""
        if self.process:
            self.process.terminate()
            await self.process.wait()
            print("\n🧹 服务器进程已清理")

async def main():
    """Main test function"""
    print("========================================")
    print("Confluence MCP Server 测试")
    print("========================================")
    
    tester = MCPTester()
    
    try:
        await tester.start_server()
        
        # Run tests
        if await tester.test_initialize():
            await tester.test_list_tools()
            await tester.test_search_pages()
            await tester.test_list_spaces()
        
        print("\n========================================")
        print("测试完成")
        print("========================================")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    finally:
        await tester.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
