# Confluence MCP Server (Python版本)

基于官方MCP Python SDK重写的Confluence集成服务器，完全兼容标准MCP协议。

## 🚀 快速开始

### 1. 环境要求
- Python 3.10 或更高版本
- 可访问的Confluence服务器

### 2. 安装

```bash
# 运行安装脚本
setup.bat

# 或手动安装
python -m venv venv
venv\Scripts\activate.bat  # Windows
pip install -r requirements.txt
```

### 3. 配置

编辑 `config.py` 文件：

```python
CONFLUENCE_BASE_URL = "http://your-confluence-server:8080/confluence"
CONFLUENCE_USERNAME = "your-username"
CONFLUENCE_PASSWORD = "your-password-or-api-token"
```

### 4. 测试

```bash
# 测试MCP服务器
python test_mcp.py

# 手动运行服务器
python confluence_mcp_server.py
```

## 🔧 Augment配置

### 方法1：<PERSON> Deskt<PERSON>配置

将以下配置添加到 `~/Library/Application Support/Claude/claude_desktop_config.json` (macOS) 或 `%APPDATA%\Claude\claude_desktop_config.json` (Windows)：

```json
{
  "mcpServers": {
    "confluence": {
      "command": "python",
      "args": [
        "C:/Users/<USER>/Desktop/idea/confluenceMcpServer/confluence_mcp_server.py"
      ],
      "env": {
        "CONFLUENCE_BASE_URL": "http://localhost:8080/confluence",
        "CONFLUENCE_USERNAME": "admin",
        "CONFLUENCE_PASSWORD": "admin"
      }
    }
  }
}
```

### 方法2：其他MCP客户端

对于其他支持MCP的客户端，使用以下命令启动服务器：

```bash
python confluence_mcp_server.py
```

服务器将通过STDIO与客户端通信。

## 🛠️ 可用工具

### 1. search_pages
搜索Confluence页面

**参数:**
- `query` (必需): 搜索关键词
- `space_key` (可选): 限制搜索的空间键
- `limit` (可选): 返回结果数量限制 (默认: 10)

**示例:**
```
请搜索包含"API文档"的页面
```

### 2. get_page_content
获取页面详细内容

**参数:**
- `page_id` (必需): 页面ID

**示例:**
```
请获取页面ID为123456的内容
```

### 3. list_spaces
列出所有空间

**参数:**
- `limit` (可选): 返回结果数量限制 (默认: 20)

**示例:**
```
请列出所有可用的Confluence空间
```

### 4. list_attachments
列出页面附件

**参数:**
- `page_id` (必需): 页面ID
- `limit` (可选): 返回结果数量限制 (默认: 10)

**示例:**
```
请列出页面123456的所有附件
```

### 5. get_page_links
提取页面链接

**参数:**
- `page_id` (必需): 页面ID

**示例:**
```
请提取页面123456中的所有链接
```

### 6. get_comments
获取页面评论

**参数:**
- `page_id` (必需): 页面ID
- `limit` (可选): 返回结果数量限制 (默认: 10)

**示例:**
```
请获取页面123456的评论
```

## 🔒 安全配置

### 使用环境变量

```bash
# Windows
set CONFLUENCE_BASE_URL=http://your-server:8080/confluence
set CONFLUENCE_USERNAME=your-username
set CONFLUENCE_PASSWORD=your-api-token

# Linux/macOS
export CONFLUENCE_BASE_URL=http://your-server:8080/confluence
export CONFLUENCE_USERNAME=your-username
export CONFLUENCE_PASSWORD=your-api-token
```

### API Token (推荐)

1. 在Confluence中生成API Token
2. 使用API Token替代密码
3. 限制Token权限到最小必要范围

## 🐛 故障排除

### 常见问题

1. **"No tools available"**
   - 检查Python路径是否正确
   - 确认虚拟环境已激活
   - 查看错误日志

2. **连接Confluence失败**
   - 验证服务器URL和凭据
   - 检查网络连接
   - 确认用户权限

3. **工具调用失败**
   - 检查页面ID是否存在
   - 确认用户有访问权限
   - 查看详细错误信息

### 调试模式

设置环境变量启用调试：

```bash
set LOG_LEVEL=DEBUG
python confluence_mcp_server.py
```

### 日志查看

- MCP服务器日志输出到stderr
- Claude Desktop日志位置：
  - macOS: `~/Library/Logs/Claude/mcp*.log`
  - Windows: `%LOCALAPPDATA%\Claude\logs\mcp*.log`

## 📊 性能优化

### 1. 连接池配置

在 `config.py` 中调整：

```python
REQUEST_TIMEOUT = 30.0  # 请求超时时间
```

### 2. 缓存策略

考虑添加缓存来减少API调用：

```python
# 可以添加简单的内存缓存
import functools
import time

@functools.lru_cache(maxsize=100)
def cached_request(url, params_hash):
    # 缓存逻辑
    pass
```

## 🔄 与Java版本的区别

| 特性 | Python版本 | Java版本 |
|------|------------|----------|
| 协议兼容性 | ✅ 标准MCP | ❌ 自定义实现 |
| 部署方式 | STDIO | WebSocket |
| 依赖管理 | pip | Maven |
| 性能 | 中等 | 高 |
| 内存占用 | 低 | 中等 |

## 📈 监控和维护

### 健康检查

```python
# 添加健康检查工具
@mcp.tool()
async def health_check() -> str:
    """检查服务器健康状态"""
    try:
        # 测试Confluence连接
        spaces = await confluence.get_spaces(1)
        return "✅ 服务器健康，Confluence连接正常"
    except Exception as e:
        return f"❌ 服务器异常: {str(e)}"
```

### 使用统计

可以添加简单的使用统计：

```python
import collections
usage_stats = collections.defaultdict(int)

# 在每个工具中添加
usage_stats[tool_name] += 1
```

现在你有了一个完全符合MCP标准的Python实现！这个版本应该能在Augment中正常工作。
