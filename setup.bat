@echo off
echo ========================================
echo Confluence MCP Server Setup
echo ========================================

echo 1. 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，请安装Python 3.10或更高版本
    pause
    exit /b 1
)

echo.
echo 2. 创建虚拟环境...
python -m venv venv
if %errorlevel% neq 0 (
    echo ❌ 创建虚拟环境失败
    pause
    exit /b 1
)

echo.
echo 3. 激活虚拟环境...
call venv\Scripts\activate.bat

echo.
echo 4. 安装依赖...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ 安装依赖失败
    pause
    exit /b 1
)

echo.
echo ✅ 设置完成！
echo.
echo 📋 下一步：
echo 1. 编辑 config.py 配置你的Confluence服务器信息
echo 2. 运行 run.bat 启动MCP服务器
echo 3. 在Augment中配置MCP服务器
echo.

pause
