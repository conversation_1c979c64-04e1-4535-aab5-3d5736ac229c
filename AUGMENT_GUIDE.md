# Augment中使用Confluence MCP服务器指南

## 🚀 快速开始

### 1. 启动MCP服务器
```bash
# 运行启动脚本
start-with-augment.bat

# 或手动启动
mvn spring-boot:run
```

### 2. 在Augment中配置

#### 方法一：手动配置
1. 打开Augment设置
2. 找到"MCP Servers"或"Model Context Protocol"设置
3. 添加新服务器：
   - **名称**: Confluence MCP Server
   - **URL**: `ws://localhost:8080/mcp`
   - **类型**: WebSocket
   - **描述**: 本地Confluence文档访问服务

#### 方法二：导入配置文件
1. 使用提供的 `augment-mcp-config.json` 配置文件
2. 在Augment中导入此配置文件

## 📖 使用方式

### 🔍 搜索和获取文档

**搜索页面**
```
请帮我搜索关于"API文档"的Confluence页面
```

**获取特定页面内容**
```
请获取页面ID为123456的Confluence页面内容
```

**列出所有空间**
```
请列出所有可用的Confluence空间
```

### 📄 资源访问

**直接访问页面资源**
```
请分析资源 confluence://page/123456 的内容
```

**访问空间资源**
```
请总结资源 confluence://space/DEV 的信息
```

### 🛠️ 工具使用示例

**搜索特定空间的页面**
```
使用search_pages工具在DEV空间中搜索"部署指南"
```

**获取页面附件**
```
使用list_attachments工具列出页面123456的所有附件
```

**提取页面链接**
```
使用get_page_links工具提取页面123456中的所有链接
```

### 💡 智能提示使用

**分析页面质量**
```
使用analyze_page提示分析页面123456的文档质量
```

**总结空间内容**
```
使用summarize_space提示总结DEV空间的整体内容
```

**比较两个页面**
```
使用compare_pages提示比较页面123456和789012的差异
```

## 🎯 实际应用场景

### 1. 文档审查和改进
```
请分析我们的API文档页面，重点关注：
1. 文档的完整性
2. 示例代码的质量
3. 可能的改进建议
```

### 2. 知识库整理
```
请总结我们开发团队空间的内容，包括：
1. 主要文档类型
2. 更新频率
3. 缺失的文档
```

### 3. 信息提取
```
从项目需求文档中提取：
1. 所有的联系人信息
2. 重要的截止日期
3. 关键的技术要求
```

### 4. 文档关联分析
```
分析这个页面中引用的所有其他文档，
并总结它们之间的关系
```

## ⚙️ 高级配置

### 自定义Confluence连接
编辑 `src/main/resources/application.yml`：

```yaml
confluence:
  base-url: http://your-confluence-server:8080/confluence
  username: your-username
  password: your-api-token
```

### 调整MCP服务器设置
```yaml
mcp:
  server-name: "My Confluence MCP Server"
  websocket-path: "/mcp"
  resources-enabled: true
  tools-enabled: true
  prompts-enabled: true
```

## 🔧 故障排除

### 连接问题
1. **检查MCP服务器状态**
   ```bash
   curl http://localhost:8080/api/health
   ```

2. **验证WebSocket连接**
   - 在浏览器中打开 `example-client.html`
   - 测试连接和基本功能

3. **检查Confluence连接**
   ```bash
   curl http://localhost:8080/api/status
   ```

### 常见错误

**"连接被拒绝"**
- 确保MCP服务器正在运行
- 检查端口8080是否被占用

**"认证失败"**
- 验证Confluence用户名和密码
- 确保用户有适当的权限

**"页面未找到"**
- 检查页面ID是否正确
- 确保用户有访问该页面的权限

## 📊 性能优化

### 1. 缓存配置
```yaml
spring:
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=10m
```

### 2. 连接池设置
```yaml
confluence:
  connect-timeout: 10000
  read-timeout: 30000
```

### 3. 日志级别调整
```yaml
logging:
  level:
    com.confluencemcpserver: INFO  # 生产环境使用INFO
```

## 🔒 安全建议

1. **使用API Token而不是密码**
2. **限制用户权限到最小必要范围**
3. **在生产环境中启用SSL**
4. **定期轮换认证凭据**
5. **监控访问日志**

## 📈 监控和维护

### 健康检查
```bash
# 定期检查服务状态
curl http://localhost:8080/api/health

# 查看详细状态
curl http://localhost:8080/api/status
```

### 日志监控
```bash
# 查看应用日志
tail -f logs/confluence-mcp-server.log
```

### 性能监控
- 监控WebSocket连接数
- 跟踪API响应时间
- 观察内存使用情况

通过以上配置，你就可以在Augment中充分利用Confluence MCP服务器的所有功能了！
