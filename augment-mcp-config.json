{"mcpServers": {"confluence": {"name": "Confluence MCP Server", "description": "访问本地Confluence实例，获取文档内容、附件和链接信息", "command": "ws://localhost:8080/mcp", "transport": {"type": "websocket"}, "capabilities": {"resources": {"enabled": true, "description": "提供Confluence页面、附件、空间等资源访问"}, "tools": {"enabled": true, "description": "提供搜索、获取内容、列出附件等工具", "tools": [{"name": "search_pages", "description": "在Confluence中搜索页面"}, {"name": "get_page_content", "description": "获取指定页面的详细内容"}, {"name": "list_attachments", "description": "列出页面的所有附件"}, {"name": "get_page_links", "description": "提取页面中的所有链接"}, {"name": "list_spaces", "description": "列出所有可访问的Confluence空间"}, {"name": "get_comments", "description": "获取页面的所有评论"}]}, "prompts": {"enabled": true, "description": "提供页面分析、空间总结等提示模板", "prompts": [{"name": "analyze_page", "description": "深入分析Confluence页面的内容、结构和关键信息"}, {"name": "summarize_space", "description": "总结Confluence空间的整体内容和结构"}, {"name": "compare_pages", "description": "比较两个Confluence页面的差异和相似性"}, {"name": "extract_key_info", "description": "从Confluence页面中提取特定类型的关键信息"}]}}, "settings": {"autoConnect": true, "reconnectOnFailure": true, "maxReconnectAttempts": 3, "healthCheckInterval": 30000}}}, "globalSettings": {"enableLogging": true, "logLevel": "INFO", "timeout": 30000}}