# Confluence MCP Server 部署指南

## 📦 打包信息

- **版本**: 1.0.0-UPGRADED
- **JAR文件**: `target/confluenceMcpServer-1.0.0-UPGRADED.jar`
- **文件大小**: ~30MB
- **主类**: `com.confluencemcpserver.McpStdioApplication`

## 🚀 快速部署

### 1. 系统要求

- **Java**: JDK 17 或更高版本
- **内存**: 最少 512MB RAM
- **磁盘**: 100MB 可用空间
- **网络**: 能够访问 Confluence 实例

### 2. 环境变量配置

必需的环境变量：

```bash
CONFLUENCE_BASE_URL=https://your-confluence-domain.atlassian.net
CONFLUENCE_USERNAME=<EMAIL>
CONFLUENCE_API_TOKEN=your-api-token
```

可选的环境变量：

```bash
SPRING_PROFILES_ACTIVE=production
MCP_TOOLS_LIST_CHANGED_ENABLED=true
MCP_RESOURCES_ENABLED=true
MCP_PROMPTS_ENABLED=true
```

### 3. 启动服务器

#### 方式一：使用启动脚本
```bash
./start-mcp-upgraded.bat
```

#### 方式二：直接运行
```bash
java -jar target/confluenceMcpServer-1.0.0-UPGRADED.jar
```

## 🔧 MCP客户端配置

### Claude Desktop 配置

将以下配置添加到 Claude Desktop 的配置文件中：

**文件位置**: 
- Windows: `%APPDATA%\Claude\claude_desktop_config.json`
- macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`

**配置内容**:
```json
{
  "mcpServers": {
    "confluence-mcp-upgraded": {
      "command": "java",
      "args": [
        "-jar",
        "C:/path/to/confluenceMcpServer-1.0.0-UPGRADED.jar"
      ],
      "env": {
        "CONFLUENCE_BASE_URL": "https://your-confluence-domain.atlassian.net",
        "CONFLUENCE_USERNAME": "<EMAIL>",
        "CONFLUENCE_API_TOKEN": "your-api-token",
        "SPRING_PROFILES_ACTIVE": "production"
      }
    }
  }
}
```

### Augment 配置

**配置文件**: `augment_mcp_config_upgraded.json`

```json
{
  "mcpServers": {
    "confluence-mcp-upgraded": {
      "command": "java",
      "args": [
        "-jar",
        "C:/path/to/confluenceMcpServer-1.0.0-UPGRADED.jar"
      ],
      "env": {
        "CONFLUENCE_BASE_URL": "https://your-confluence-domain.atlassian.net",
        "CONFLUENCE_USERNAME": "<EMAIL>", 
        "CONFLUENCE_API_TOKEN": "your-api-token",
        "SPRING_PROFILES_ACTIVE": "production",
        "MCP_TOOLS_LIST_CHANGED_ENABLED": "true",
        "MCP_RESOURCES_ENABLED": "true",
        "MCP_PROMPTS_ENABLED": "true"
      }
    }
  }
}
```

## 🔑 Confluence API Token 获取

1. 登录到 Atlassian 账户
2. 访问 [API Tokens 页面](https://id.atlassian.com/manage-profile/security/api-tokens)
3. 点击 "Create API token"
4. 输入标签名称（如 "MCP Server"）
5. 复制生成的 token

## 🧪 测试部署

### 1. 运行测试脚本
```bash
./test-mcp-upgraded.bat
```

### 2. 手动测试

发送初始化请求：
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "initialize",
  "params": {
    "protocolVersion": "2025-06-18",
    "clientInfo": {
      "name": "Test Client",
      "version": "1.0.0"
    }
  }
}
```

测试工具列表：
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "method": "tools/list",
  "params": {}
}
```

测试连接：
```json
{
  "jsonrpc": "2.0",
  "id": 3,
  "method": "tools/call",
  "params": {
    "name": "test_connection",
    "arguments": {}
  }
}
```

## 📊 新功能验证

### 1. 分页功能测试

测试工具列表分页：
```json
{
  "jsonrpc": "2.0",
  "id": 4,
  "method": "tools/list",
  "params": {
    "cursor": "0"
  }
}
```

### 2. 输出模式验证

检查工具定义中的 `outputSchema` 字段：
```bash
java -jar target/confluenceMcpServer-1.0.0-UPGRADED.jar < test_tools_list.json
```

### 3. 工具注解验证

验证每个工具都包含：
- `annotations.audience`
- `annotations.priority`
- `annotations.category`

## 🔍 故障排除

### 常见问题

1. **连接失败**
   - 检查 Confluence URL 是否正确
   - 验证 API Token 是否有效
   - 确认网络连接

2. **权限错误**
   - 确认用户有访问 Confluence 的权限
   - 检查 API Token 的权限范围

3. **Java 版本问题**
   - 确保使用 JDK 17 或更高版本
   - 检查 JAVA_HOME 环境变量

### 日志查看

服务器日志会输出到控制台，包含：
- 启动信息
- 请求处理日志
- 错误信息

## 📈 性能优化

### JVM 参数建议

```bash
java -Xmx1g -Xms512m -XX:+UseG1GC -jar confluenceMcpServer-1.0.0-UPGRADED.jar
```

### 生产环境配置

```bash
export SPRING_PROFILES_ACTIVE=production
export LOGGING_LEVEL_ROOT=WARN
export LOGGING_LEVEL_COM_CONFLUENCEMCPSERVER=INFO
```

## 🔄 升级说明

此版本包含以下升级：

- ✅ 符合 MCP 2025-06-18 规范
- ✅ 支持工具列表分页
- ✅ 添加输出模式定义
- ✅ 支持工具注解
- ✅ 工具列表变更通知
- ✅ 向后兼容性

## 📞 支持

如需技术支持，请参考：
- `MCP_TOOLS_UPGRADE.md` - 详细升级文档
- `UPGRADE_SUMMARY.md` - 升级总结
- `ToolHandlerTest.java` - 测试用例

---

**部署完成后，请重启 MCP 客户端以加载新的服务器配置。**
