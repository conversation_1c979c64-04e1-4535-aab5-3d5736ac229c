#!/usr/bin/env python3
"""
Quick test for MCP server
"""

import json
import subprocess
import sys
import time

def test_mcp():
    """Quick test of MCP server"""
    print("🧪 快速测试MCP服务器...")
    
    # Start server
    process = subprocess.Popen(
        [sys.executable, "minimal_mcp.py"],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    try:
        # Send initialize
        init_msg = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2025-06-18",
                "clientInfo": {"name": "Test", "version": "1.0.0"},
                "capabilities": {}
            }
        }
        
        print("📤 发送初始化消息...")
        process.stdin.write(json.dumps(init_msg) + "\n")
        process.stdin.flush()
        
        # Read response with timeout
        import select
        import os
        
        if os.name == 'nt':  # Windows
            # On Windows, use a simple timeout
            time.sleep(2)
            if process.poll() is None:
                # Process still running, try to read
                try:
                    output = process.stdout.readline()
                    if output:
                        print(f"📨 收到响应: {output.strip()}")
                        response = json.loads(output.strip())
                        if "result" in response:
                            print("✅ 初始化成功!")
                            
                            # Test tools list
                            tools_msg = {
                                "jsonrpc": "2.0",
                                "id": 2,
                                "method": "tools/list",
                                "params": {}
                            }
                            
                            print("📤 请求工具列表...")
                            process.stdin.write(json.dumps(tools_msg) + "\n")
                            process.stdin.flush()
                            
                            time.sleep(1)
                            tools_output = process.stdout.readline()
                            if tools_output:
                                print(f"📨 工具列表响应: {tools_output.strip()}")
                                tools_response = json.loads(tools_output.strip())
                                if "result" in tools_response and "tools" in tools_response["result"]:
                                    tools = tools_response["result"]["tools"]
                                    print(f"✅ 找到 {len(tools)} 个工具:")
                                    for tool in tools:
                                        print(f"   - {tool['name']}")
                                    return True
                        else:
                            print(f"❌ 初始化失败: {response}")
                    else:
                        print("❌ 没有收到响应")
                except Exception as e:
                    print(f"❌ 解析响应失败: {e}")
            else:
                print("❌ 服务器进程已退出")
        
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        process.terminate()
        process.wait()

if __name__ == "__main__":
    if test_mcp():
        print("\n🎉 MCP服务器测试通过!")
        print("\n📋 下一步:")
        print("1. 在Augment中配置MCP服务器")
        print("2. 使用配置文件: claude_desktop_config.json")
        print("3. 重启Augment")
    else:
        print("\n❌ MCP服务器测试失败")
        print("请检查错误信息并修复问题")
