@echo off
echo ========================================
echo 验证 Confluence MCP Server 打包
echo ========================================
echo.

REM 检查JAR文件是否存在
if exist "target\confluenceMcpServer-1.0.0-UPGRADED.jar" (
    echo ✅ JAR文件存在: confluenceMcpServer-1.0.0-UPGRADED.jar
    
    REM 获取文件大小
    for %%A in ("target\confluenceMcpServer-1.0.0-UPGRADED.jar") do (
        echo    文件大小: %%~zA 字节
    )
    
    REM 检查文件修改时间
    for %%A in ("target\confluenceMcpServer-1.0.0-UPGRADED.jar") do (
        echo    修改时间: %%~tA
    )
    echo.
) else (
    echo ❌ JAR文件不存在！
    echo 请先运行: ./mvnw.cmd package -DskipTests
    goto :end
)

REM 检查Java版本
echo 检查Java环境:
java -version 2>&1 | findstr "version"
if %errorlevel% neq 0 (
    echo ❌ Java未安装或不在PATH中
    goto :end
) else (
    echo ✅ Java环境正常
)
echo.

REM 检查JAR文件内容
echo 检查JAR文件内容:
jar -tf "target\confluenceMcpServer-1.0.0-UPGRADED.jar" | findstr "McpStdioApplication.class" >nul
if %errorlevel% equ 0 (
    echo ✅ 主类文件存在: McpStdioApplication.class
) else (
    echo ❌ 主类文件缺失
)

jar -tf "target\confluenceMcpServer-1.0.0-UPGRADED.jar" | findstr "ToolHandler.class" >nul
if %errorlevel% equ 0 (
    echo ✅ 工具处理器存在: ToolHandler.class
) else (
    echo ❌ 工具处理器缺失
)

jar -tf "target\confluenceMcpServer-1.0.0-UPGRADED.jar" | findstr "application.yml" >nul
if %errorlevel% equ 0 (
    echo ✅ 配置文件存在: application.yml
) else (
    echo ❌ 配置文件缺失
)
echo.

REM 快速启动测试
echo 进行快速启动测试:
echo 设置测试环境变量...
set CONFLUENCE_BASE_URL=https://test.atlassian.net
set CONFLUENCE_USERNAME=<EMAIL>
set CONFLUENCE_API_TOKEN=test-token

echo 启动服务器进行快速测试...
timeout /t 2 >nul

REM 创建测试输入
echo {"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2025-06-18","clientInfo":{"name":"Verify Client","version":"1.0.0"}}} > verify_input.json

echo 测试输入已创建: verify_input.json
echo.

echo ========================================
echo 验证完成
echo ========================================
echo.
echo 下一步:
echo 1. 配置Confluence连接信息
echo 2. 运行: start-mcp-upgraded.bat
echo 3. 配置MCP客户端
echo.

:end
pause
