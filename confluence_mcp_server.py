#!/usr/bin/env python3
"""
Confluence MCP Server

A Model Context Protocol server that provides access to Confluence pages, spaces, and attachments.
"""

import asyncio
import logging
import sys
from typing import Any, Dict, List, Optional
import httpx
from mcp.server.fastmcp import FastMCP
import base64
import re
from urllib.parse import urljoin
from config import CONFLUENCE_BASE_URL, CONFLUENCE_USERNAME, CONFLUENCE_PASSWORD, REQUEST_TIMEOUT, LOG_LEVEL

# Configure logging to stderr (not stdout for MCP)
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stderr)]
)
logger = logging.getLogger(__name__)

# Initialize FastMCP server
mcp = FastMCP("confluence")

class ConfluenceClient:
    """Confluence REST API client"""
    
    def __init__(self, base_url: str, username: str, password: str):
        self.base_url = base_url.rstrip('/')
        self.api_base = f"{self.base_url}/rest/api"
        self.auth = (username, password)
        self.headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
    
    async def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Optional[Dict[str, Any]]:
        """Make HTTP request to Confluence API"""
        url = f"{self.api_base}/{endpoint.lstrip('/')}"
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(
                    url,
                    auth=self.auth,
                    headers=self.headers,
                    params=params or {},
                    timeout=REQUEST_TIMEOUT
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"HTTP error for {url}: {e}")
                return None
            except Exception as e:
                logger.error(f"Request failed for {url}: {e}")
                return None
    
    async def search_pages(self, query: str, space_key: Optional[str] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Search for pages in Confluence"""
        params = {
            "cql": f"text ~ \"{query}\"",
            "limit": limit,
            "expand": "space,version,body.view"
        }
        
        if space_key:
            params["cql"] = f"space = {space_key} AND text ~ \"{query}\""
        
        try:
            data = await self._make_request("content/search", params)
            if data and "results" in data:
                return data["results"]
        except Exception as e:
            logger.error(f"Search failed: {e}")
        
        return []
    
    async def get_page(self, page_id: str) -> Optional[Dict[str, Any]]:
        """Get page by ID"""
        params = {
            "expand": "body.storage,body.view,space,version,history,ancestors,children.page,children.attachment,children.comment"
        }
        return await self._make_request(f"content/{page_id}", params)
    
    async def get_spaces(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get all spaces"""
        params = {
            "limit": limit,
            "expand": "description.plain,homepage"
        }
        
        try:
            data = await self._make_request("space", params)
            if data and "results" in data:
                return data["results"]
        except Exception as e:
            logger.error(f"Get spaces failed: {e}")
        
        return []
    
    async def get_page_attachments(self, page_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get attachments for a page"""
        params = {
            "limit": limit,
            "expand": "metadata"
        }
        
        try:
            data = await self._make_request(f"content/{page_id}/child/attachment", params)
            if data and "results" in data:
                return data["results"]
        except Exception as e:
            logger.error(f"Get attachments failed: {e}")
        
        return []
    
    async def get_page_comments(self, page_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get comments for a page"""
        params = {
            "limit": limit,
            "expand": "body.view"
        }
        
        try:
            data = await self._make_request(f"content/{page_id}/child/comment", params)
            if data and "results" in data:
                return data["results"]
        except Exception as e:
            logger.error(f"Get comments failed: {e}")
        
        return []

# Initialize Confluence client
confluence = ConfluenceClient(CONFLUENCE_BASE_URL, CONFLUENCE_USERNAME, CONFLUENCE_PASSWORD)

def format_page_summary(page: Dict[str, Any]) -> str:
    """Format page information for display"""
    title = page.get("title", "Unknown")
    page_id = page.get("id", "Unknown")
    space_name = page.get("space", {}).get("name", "Unknown")
    
    # Get content preview
    content = ""
    if page.get("body") and page["body"].get("view"):
        raw_content = page["body"]["view"].get("value", "")
        # Strip HTML tags for preview
        clean_content = re.sub(r'<[^>]+>', '', raw_content)
        content = clean_content[:200] + "..." if len(clean_content) > 200 else clean_content
    
    return f"""📄 {title}
   ID: {page_id}
   Space: {space_name}
   Preview: {content}"""

def extract_links_from_content(content: str) -> List[str]:
    """Extract links from HTML content"""
    links = []
    
    # Extract HTTP/HTTPS links
    http_pattern = r'https?://[^\s<>"\']+[^\s<>"\'.,)]'
    http_links = re.findall(http_pattern, content)
    links.extend(http_links)
    
    # Extract Confluence internal links
    confluence_pattern = r'<ri:page ri:content-title="([^"]+)"'
    internal_links = re.findall(confluence_pattern, content)
    links.extend([f"Internal page: {link}" for link in internal_links])
    
    return list(set(links))  # Remove duplicates

def format_file_size(bytes_size: int) -> str:
    """Format file size in human readable format"""
    if bytes_size < 1024:
        return f"{bytes_size} B"
    elif bytes_size < 1024 * 1024:
        return f"{bytes_size / 1024:.1f} KB"
    elif bytes_size < 1024 * 1024 * 1024:
        return f"{bytes_size / (1024 * 1024):.1f} MB"
    else:
        return f"{bytes_size / (1024 * 1024 * 1024):.1f} GB"

# MCP Tools Implementation

@mcp.tool()
async def search_pages(query: str, space_key: Optional[str] = None, limit: int = 10) -> str:
    """Search for pages in Confluence.
    
    Args:
        query: Search query string
        space_key: Optional space key to limit search (e.g., 'DEV', 'DOC')
        limit: Maximum number of results to return (default: 10)
    """
    try:
        pages = await confluence.search_pages(query, space_key, limit)
        
        if not pages:
            return f"No pages found for query: '{query}'" + (f" in space '{space_key}'" if space_key else "")
        
        result = f"🔍 Found {len(pages)} page(s) for '{query}'" + (f" in space '{space_key}'" if space_key else "") + ":\n\n"
        
        for page in pages:
            result += format_page_summary(page) + "\n\n"
        
        return result.strip()
        
    except Exception as e:
        logger.error(f"Search pages error: {e}")
        return f"Error searching pages: {str(e)}"

@mcp.tool()
async def get_page_content(page_id: str) -> str:
    """Get detailed content of a specific Confluence page.
    
    Args:
        page_id: The ID of the page to retrieve
    """
    try:
        page = await confluence.get_page(page_id)
        
        if not page:
            return f"Page with ID '{page_id}' not found or access denied."
        
        title = page.get("title", "Unknown")
        space_name = page.get("space", {}).get("name", "Unknown")
        version = page.get("version", {}).get("number", "Unknown")
        
        result = f"""📄 Page: {title}
ID: {page_id}
Space: {space_name}
Version: {version}

Content:
{'=' * 50}
"""
        
        # Get page content
        if page.get("body") and page["body"].get("view"):
            content = page["body"]["view"].get("value", "No content available")
            result += content
        elif page.get("body") and page["body"].get("storage"):
            content = page["body"]["storage"].get("value", "No content available")
            result += content
        else:
            result += "No content available"
        
        return result
        
    except Exception as e:
        logger.error(f"Get page content error: {e}")
        return f"Error retrieving page content: {str(e)}"

@mcp.tool()
async def list_spaces(limit: int = 20) -> str:
    """List all available Confluence spaces.
    
    Args:
        limit: Maximum number of spaces to return (default: 20)
    """
    try:
        spaces = await confluence.get_spaces(limit)
        
        if not spaces:
            return "No spaces found or access denied."
        
        result = f"📁 Found {len(spaces)} space(s):\n\n"
        
        for space in spaces:
            name = space.get("name", "Unknown")
            key = space.get("key", "Unknown")
            space_type = space.get("type", "Unknown")
            
            description = "No description"
            if space.get("description") and space["description"].get("plain"):
                desc_text = space["description"]["plain"].get("value", "")
                description = desc_text[:100] + "..." if len(desc_text) > 100 else desc_text
            
            result += f"""📁 {name}
   Key: {key}
   Type: {space_type}
   Description: {description}

"""
        
        return result.strip()
        
    except Exception as e:
        logger.error(f"List spaces error: {e}")
        return f"Error listing spaces: {str(e)}"

@mcp.tool()
async def list_attachments(page_id: str, limit: int = 10) -> str:
    """List attachments for a specific page.
    
    Args:
        page_id: The ID of the page
        limit: Maximum number of attachments to return (default: 10)
    """
    try:
        attachments = await confluence.get_page_attachments(page_id, limit)
        
        if not attachments:
            return f"No attachments found for page ID '{page_id}'."
        
        result = f"📎 Found {len(attachments)} attachment(s) for page {page_id}:\n\n"
        
        for attachment in attachments:
            title = attachment.get("title", "Unknown")
            att_id = attachment.get("id", "Unknown")
            
            # Get file info
            file_size = "Unknown"
            media_type = "Unknown"
            
            if attachment.get("extensions"):
                ext = attachment["extensions"]
                if ext.get("fileSize"):
                    file_size = format_file_size(ext["fileSize"])
                if ext.get("mediaType"):
                    media_type = ext["mediaType"]
            
            # Get download link
            download_link = "Not available"
            if attachment.get("_links") and attachment["_links"].get("download"):
                download_link = urljoin(CONFLUENCE_BASE_URL, attachment["_links"]["download"])
            
            result += f"""📎 {title}
   ID: {att_id}
   Type: {media_type}
   Size: {file_size}
   Download: {download_link}

"""
        
        return result.strip()
        
    except Exception as e:
        logger.error(f"List attachments error: {e}")
        return f"Error listing attachments: {str(e)}"

@mcp.tool()
async def get_page_links(page_id: str) -> str:
    """Extract all links from a page's content.
    
    Args:
        page_id: The ID of the page
    """
    try:
        page = await confluence.get_page(page_id)
        
        if not page:
            return f"Page with ID '{page_id}' not found or access denied."
        
        title = page.get("title", "Unknown")
        
        # Get page content for link extraction
        content = ""
        if page.get("body") and page["body"].get("storage"):
            content = page["body"]["storage"].get("value", "")
        elif page.get("body") and page["body"].get("view"):
            content = page["body"]["view"].get("value", "")
        
        if not content:
            return f"No content available for page '{title}' to extract links from."
        
        links = extract_links_from_content(content)
        
        if not links:
            return f"No links found in page '{title}'."
        
        result = f"🔗 Found {len(links)} link(s) in page '{title}':\n\n"
        
        for i, link in enumerate(links, 1):
            result += f"{i}. {link}\n"
        
        return result.strip()
        
    except Exception as e:
        logger.error(f"Get page links error: {e}")
        return f"Error extracting page links: {str(e)}"

@mcp.tool()
async def get_comments(page_id: str, limit: int = 10) -> str:
    """Get comments for a specific page.
    
    Args:
        page_id: The ID of the page
        limit: Maximum number of comments to return (default: 10)
    """
    try:
        comments = await confluence.get_page_comments(page_id, limit)
        
        if not comments:
            return f"No comments found for page ID '{page_id}'."
        
        result = f"💬 Found {len(comments)} comment(s) for page {page_id}:\n\n"
        
        for comment in comments:
            title = comment.get("title", "Comment")
            comment_id = comment.get("id", "Unknown")
            
            # Get comment content
            content = "No content"
            if comment.get("body") and comment["body"].get("view"):
                raw_content = comment["body"]["view"].get("value", "")
                # Strip HTML tags
                clean_content = re.sub(r'<[^>]+>', '', raw_content)
                content = clean_content[:200] + "..." if len(clean_content) > 200 else clean_content
            
            result += f"""💬 {title}
   ID: {comment_id}
   Content: {content}

"""
        
        return result.strip()
        
    except Exception as e:
        logger.error(f"Get comments error: {e}")
        return f"Error retrieving comments: {str(e)}"

if __name__ == "__main__":
    # Run the MCP server
    mcp.run(transport="stdio")
