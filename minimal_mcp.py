#!/usr/bin/env python3
"""
Minimal MCP Server for testing
"""

from mcp.server.fastmcp import FastMCP

# Initialize FastMCP server
mcp = FastMCP("test-confluence")

@mcp.tool()
async def hello_world() -> str:
    """A simple hello world tool for testing.
    
    Returns:
        A greeting message
    """
    return "Hello from Confluence MCP Server! 🎉"

@mcp.tool()
async def search_pages(query: str, limit: int = 5) -> str:
    """Mock search pages tool.
    
    Args:
        query: Search query
        limit: Number of results
    """
    return f"""🔍 模拟搜索结果 (查询: "{query}", 限制: {limit})

📄 API文档 - 系统接口说明
   ID: mock-001
   空间: DEV
   
📄 用户指南 - 操作手册  
   ID: mock-002
   空间: DOC

注意: 这是模拟数据，用于测试MCP连接。"""

@mcp.tool()
async def list_spaces(limit: int = 10) -> str:
    """Mock list spaces tool.
    
    Args:
        limit: Number of spaces to return
    """
    return f"""📁 模拟空间列表 (限制: {limit})

📁 DEV - 开发团队空间
   键: DEV
   类型: global
   
📁 DOC - 文档中心
   键: DOC  
   类型: global

注意: 这是模拟数据，用于测试MCP连接。"""

if __name__ == "__main__":
    # Don't print to stdout as it interferes with MCP protocol
    import sys
    print("Starting minimal MCP server...", file=sys.stderr, flush=True)
    mcp.run(transport="stdio")
