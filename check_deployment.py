#!/usr/bin/env python3
"""
Deployment checker for Confluence MCP Server
"""

import sys
import subprocess
import json
import os
from pathlib import Path

def check_python():
    """Check Python version"""
    print("🐍 检查Python环境...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 10:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - 版本符合要求")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - 需要Python 3.10+")
        return False

def check_dependencies():
    """Check required dependencies"""
    print("\n📦 检查依赖包...")
    required_packages = ["mcp", "httpx"]
    
    all_ok = True
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装")
            all_ok = False
    
    if not all_ok:
        print("\n💡 安装缺失的依赖:")
        print("pip install mcp httpx")
    
    return all_ok

def check_files():
    """Check required files"""
    print("\n📁 检查必需文件...")
    required_files = [
        "minimal_mcp.py",
        "confluence_mcp_server.py", 
        "config.py",
        "claude_desktop_config.json"
    ]
    
    all_ok = True
    for file in required_files:
        if Path(file).exists():
            print(f"✅ {file} - 存在")
        else:
            print(f"❌ {file} - 缺失")
            all_ok = False
    
    return all_ok

def check_config():
    """Check configuration"""
    print("\n⚙️ 检查配置...")
    
    try:
        import config
        print(f"✅ Confluence URL: {config.CONFLUENCE_BASE_URL}")
        print(f"✅ Username: {config.CONFLUENCE_USERNAME}")
        print(f"✅ Password: {'*' * len(config.CONFLUENCE_PASSWORD)}")
        return True
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def check_mcp_server():
    """Test MCP server startup"""
    print("\n🚀 测试MCP服务器启动...")
    
    try:
        # Test minimal server
        process = subprocess.Popen(
            [sys.executable, "minimal_mcp.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Send initialize message
        init_message = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2025-06-18",
                "clientInfo": {"name": "Test", "version": "1.0.0"},
                "capabilities": {}
            }
        }
        
        message_json = json.dumps(init_message) + "\n"
        stdout, stderr = process.communicate(input=message_json, timeout=5)
        
        if stdout:
            try:
                response = json.loads(stdout.strip())
                if "result" in response:
                    print("✅ MCP服务器启动成功")
                    return True
                else:
                    print(f"❌ MCP服务器响应异常: {response}")
                    return False
            except json.JSONDecodeError:
                print(f"❌ MCP服务器响应格式错误: {stdout}")
                return False
        else:
            print(f"❌ MCP服务器无响应，错误: {stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ MCP服务器启动超时")
        process.kill()
        return False
    except Exception as e:
        print(f"❌ MCP服务器测试失败: {e}")
        return False

def generate_augment_config():
    """Generate Augment configuration"""
    print("\n📝 生成Augment配置...")
    
    current_dir = Path.cwd()
    config = {
        "mcpServers": {
            "confluence": {
                "command": "python",
                "args": [str(current_dir / "minimal_mcp.py")],
                "env": {
                    "CONFLUENCE_BASE_URL": "http://localhost:8080/confluence",
                    "CONFLUENCE_USERNAME": "admin",
                    "CONFLUENCE_PASSWORD": "admin"
                }
            }
        }
    }
    
    config_file = "augment_config.json"
    with open(config_file, "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 配置已生成: {config_file}")
    print("\n📋 在Augment中使用此配置:")
    print(f"1. 复制 {config_file} 的内容")
    print("2. 粘贴到Augment的MCP服务器配置中")
    print("3. 重启Augment")

def main():
    """Main check function"""
    print("========================================")
    print("Confluence MCP Server 部署检查")
    print("========================================")
    
    checks = [
        ("Python环境", check_python),
        ("依赖包", check_dependencies),
        ("必需文件", check_files),
        ("配置", check_config),
        ("MCP服务器", check_mcp_server)
    ]
    
    results = []
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}检查失败: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "="*40)
    print("检查结果汇总")
    print("="*40)
    
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有检查通过！")
        generate_augment_config()
        print("\n🚀 现在可以在Augment中配置MCP服务器了！")
    else:
        print("\n⚠️ 部分检查失败，请修复后重试")
        print("\n💡 常见解决方案:")
        print("- 安装依赖: pip install mcp httpx")
        print("- 检查文件是否完整")
        print("- 验证配置信息")

if __name__ == "__main__":
    main()
