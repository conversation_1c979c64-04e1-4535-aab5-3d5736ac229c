@echo off
echo ========================================
echo 启动Confluence MCP服务器并配置Augment
echo ========================================

echo 1. 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo ❌ 未找到Java环境，请确保已安装Java 17或更高版本
    pause
    exit /b 1
)

echo.
echo 2. 启动Confluence MCP服务器...
echo 正在后台启动服务器...
start /B mvn spring-boot:run

echo.
echo 3. 等待服务器启动...
timeout /t 10 /nobreak

echo.
echo 4. 检查服务器状态...
curl -s http://localhost:8080/api/health
if %errorlevel% neq 0 (
    echo ❌ 服务器启动失败，请检查日志
    pause
    exit /b 1
)

echo.
echo ✅ MCP服务器已启动成功！
echo.
echo 📋 配置信息：
echo   - WebSocket端点: ws://localhost:8080/mcp
echo   - 健康检查: http://localhost:8080/api/health
echo   - 配置文件: augment-mcp-config.json
echo.
echo 🔧 在Augment中配置MCP服务器：
echo   1. 打开Augment设置
echo   2. 找到MCP Servers配置
echo   3. 添加新服务器：
echo      名称: Confluence MCP Server
echo      URL: ws://localhost:8080/mcp
echo      类型: WebSocket
echo.
echo 或者导入配置文件: augment-mcp-config.json
echo.
echo 按任意键继续...
pause

echo.
echo 🚀 现在可以在Augment中使用以下功能：
echo.
echo 📄 资源访问：
echo   - confluence://page/{pageId} - 访问页面内容
echo   - confluence://space/{spaceKey} - 访问空间信息
echo   - confluence://attachment/{attachmentId} - 访问附件
echo.
echo 🛠️ 工具使用：
echo   - search_pages - 搜索页面
echo   - get_page_content - 获取页面内容
echo   - list_spaces - 列出空间
echo   - list_attachments - 列出附件
echo   - get_page_links - 获取页面链接
echo   - get_comments - 获取评论
echo.
echo 💡 提示模板：
echo   - analyze_page - 分析页面
echo   - summarize_space - 总结空间
echo   - compare_pages - 比较页面
echo   - extract_key_info - 提取关键信息
echo.

pause
