package com.confluencemcpserver.controller;

import com.confluencemcpserver.config.ConfluenceConfig;
import com.confluencemcpserver.config.McpConfig;
import com.confluencemcpserver.confluence.ConfluenceClient;
import com.confluencemcpserver.mcp.transport.WebSocketTransport;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 健康检查控制器测试
 */
@WebMvcTest(HealthController.class)
class HealthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ConfluenceConfig confluenceConfig;

    @MockBean
    private McpConfig mcpConfig;

    @MockBean
    private WebSocketTransport webSocketTransport;

    @MockBean
    private ConfluenceClient confluenceClient;

    @Test
    void testHealthEndpoint() throws Exception {
        // Mock配置
        when(mcpConfig.getServerName()).thenReturn("Test MCP Server");
        when(mcpConfig.getServerVersion()).thenReturn("1.0.0-test");

        mockMvc.perform(get("/api/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("UP"))
                .andExpect(jsonPath("$.timestamp").exists())
                .andExpect(jsonPath("$.server.name").value("Test MCP Server"))
                .andExpect(jsonPath("$.server.version").value("1.0.0-test"));
    }

    @Test
    void testStatusEndpoint() throws Exception {
        // Mock配置
        when(mcpConfig.getServerName()).thenReturn("Test MCP Server");
        when(mcpConfig.getServerVersion()).thenReturn("1.0.0-test");
        when(mcpConfig.getWebsocketPath()).thenReturn("/mcp");
        when(mcpConfig.isResourcesEnabled()).thenReturn(true);
        when(mcpConfig.isToolsEnabled()).thenReturn(true);
        when(mcpConfig.isPromptsEnabled()).thenReturn(true);

        when(confluenceConfig.getBaseUrl()).thenReturn("http://localhost:8080/confluence");
        when(confluenceConfig.getUsername()).thenReturn("test-user");
        when(confluenceConfig.getConnectTimeout()).thenReturn(30000);
        when(confluenceConfig.getReadTimeout()).thenReturn(60000);
        when(confluenceConfig.isSslVerification()).thenReturn(true);

        when(webSocketTransport.getActiveSessionCount()).thenReturn(2);

        mockMvc.perform(get("/api/status"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.server.name").value("Test MCP Server"))
                .andExpect(jsonPath("$.confluence.baseUrl").value("http://localhost:8080/confluence"))
                .andExpect(jsonPath("$.confluence.username").value("test-user"))
                .andExpect(jsonPath("$.mcp.resourcesEnabled").value(true))
                .andExpect(jsonPath("$.websocket.activeSessions").value(2));
    }

    @Test
    void testInfoEndpoint() throws Exception {
        // Mock配置
        when(mcpConfig.getServerVersion()).thenReturn("1.0.0-test");
        when(mcpConfig.getWebsocketPath()).thenReturn("/mcp");

        mockMvc.perform(get("/api/info"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.application.name").value("Confluence MCP Server"))
                .andExpect(jsonPath("$.application.version").value("1.0.0-test"))
                .andExpect(jsonPath("$.features.resources").exists())
                .andExpect(jsonPath("$.features.tools").exists())
                .andExpect(jsonPath("$.features.prompts").exists())
                .andExpect(jsonPath("$.endpoints.websocket").value("ws://localhost:8080/mcp"));
    }
}
