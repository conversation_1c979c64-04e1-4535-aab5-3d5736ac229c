package com.confluencemcpserver.mcp.transport;

import com.confluencemcpserver.mcp.McpServer;
import com.confluencemcpserver.mcp.protocol.JsonRpcMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import reactor.core.publisher.Mono;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * WebSocket传输层测试
 */
@ExtendWith(MockitoExtension.class)
class WebSocketTransportTest {

    @Mock
    private McpServer mcpServer;

    @Mock
    private WebSocketSession session;

    private WebSocketTransport webSocketTransport;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        webSocketTransport = new WebSocketTransport(mcpServer);
        objectMapper = new ObjectMapper();
        
        when(session.getId()).thenReturn("test-session-123");
        when(session.isOpen()).thenReturn(true);
    }

    @Test
    void testConnectionEstablished() throws Exception {
        // 执行连接建立
        webSocketTransport.afterConnectionEstablished(session);

        // 验证会话被添加
        assertEquals(1, webSocketTransport.getActiveSessionCount());

        // 验证发送了欢迎消息
        verify(session, atLeastOnce()).sendMessage(any(TextMessage.class));
    }

    @Test
    void testConnectionClosed() throws Exception {
        // 先建立连接
        webSocketTransport.afterConnectionEstablished(session);
        assertEquals(1, webSocketTransport.getActiveSessionCount());

        // 关闭连接
        webSocketTransport.afterConnectionClosed(session, CloseStatus.NORMAL);

        // 验证会话被移除
        assertEquals(0, webSocketTransport.getActiveSessionCount());
    }

    @Test
    void testHandleValidMessage() throws Exception {
        // 准备Mock响应
        JsonRpcMessage mockResponse = new JsonRpcMessage();
        mockResponse.setId(1);
        mockResponse.setResult(Map.of("status", "success"));
        
        when(mcpServer.handleMessage(any(JsonRpcMessage.class)))
                .thenReturn(Mono.just(mockResponse));

        // 准备请求消息
        JsonRpcMessage request = new JsonRpcMessage();
        request.setId(1);
        request.setMethod("initialize");
        request.setParams(Map.of("protocolVersion", "2025-06-18"));

        String requestJson = objectMapper.writeValueAsString(request);
        TextMessage message = new TextMessage(requestJson);

        // 建立连接
        webSocketTransport.afterConnectionEstablished(session);

        // 处理消息
        webSocketTransport.handleMessage(session, message);

        // 验证调用了MCP服务器
        verify(mcpServer).handleMessage(any(JsonRpcMessage.class));
    }

    @Test
    void testHandleInvalidMessage() throws Exception {
        // 建立连接
        webSocketTransport.afterConnectionEstablished(session);

        // 发送无效JSON
        TextMessage invalidMessage = new TextMessage("invalid json");

        // 处理消息（不应该抛出异常）
        assertDoesNotThrow(() -> {
            webSocketTransport.handleMessage(session, invalidMessage);
        });

        // 验证发送了错误响应
        verify(session, atLeast(2)).sendMessage(any(TextMessage.class)); // 欢迎消息 + 错误响应
    }

    @Test
    void testBroadcastNotification() throws Exception {
        // 建立多个连接
        WebSocketSession session1 = mock(WebSocketSession.class);
        WebSocketSession session2 = mock(WebSocketSession.class);
        
        when(session1.getId()).thenReturn("session-1");
        when(session1.isOpen()).thenReturn(true);
        when(session2.getId()).thenReturn("session-2");
        when(session2.isOpen()).thenReturn(true);

        webSocketTransport.afterConnectionEstablished(session1);
        webSocketTransport.afterConnectionEstablished(session2);

        assertEquals(2, webSocketTransport.getActiveSessionCount());

        // 广播通知
        Map<String, Object> params = Map.of("message", "test notification");
        webSocketTransport.broadcastNotification("notifications/test", params);

        // 验证所有会话都收到了通知
        verify(session1, atLeast(2)).sendMessage(any(TextMessage.class)); // 欢迎消息 + 通知
        verify(session2, atLeast(2)).sendMessage(any(TextMessage.class)); // 欢迎消息 + 通知
    }

    @Test
    void testTransportError() throws Exception {
        Exception testException = new RuntimeException("Test transport error");

        // 建立连接
        webSocketTransport.afterConnectionEstablished(session);

        // 处理传输错误（不应该抛出异常）
        assertDoesNotThrow(() -> {
            webSocketTransport.handleTransportError(session, testException);
        });
    }
}
