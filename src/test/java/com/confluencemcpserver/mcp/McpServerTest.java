package com.confluencemcpserver.mcp;

import com.confluencemcpserver.config.ConfluenceConfig;
import com.confluencemcpserver.config.McpConfig;
import com.confluencemcpserver.confluence.ConfluenceClient;
import com.confluencemcpserver.confluence.model.Page;
import com.confluencemcpserver.confluence.model.PageList;
import com.confluencemcpserver.confluence.model.Space;
import com.confluencemcpserver.mcp.handlers.ResourceHandler;
import com.confluencemcpserver.mcp.handlers.ToolHandler;
import com.confluencemcpserver.mcp.handlers.PromptHandler;
import com.confluencemcpserver.mcp.protocol.JsonRpcMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MCP服务器测试
 */
@ExtendWith(MockitoExtension.class)
class McpServerTest {

    @Mock
    private ConfluenceClient confluenceClient;

    private McpServer mcpServer;
    private McpConfig mcpConfig;
    private ResourceHandler resourceHandler;
    private ToolHandler toolHandler;
    private PromptHandler promptHandler;

    @BeforeEach
    void setUp() {
        mcpConfig = new McpConfig();
        mcpConfig.setServerName("Test MCP Server");
        mcpConfig.setServerVersion("1.0.0-test");
        mcpConfig.setResourcesEnabled(true);
        mcpConfig.setToolsEnabled(true);
        mcpConfig.setPromptsEnabled(true);

        resourceHandler = new ResourceHandler(confluenceClient);
        toolHandler = new ToolHandler(confluenceClient);
        promptHandler = new PromptHandler(confluenceClient);
        
        mcpServer = new McpServer(mcpConfig, resourceHandler, toolHandler, promptHandler);
    }

    @Test
    void testInitialize() {
        // 准备请求
        JsonRpcMessage request = new JsonRpcMessage();
        request.setId(1);
        request.setMethod("initialize");
        request.setParams(Map.of(
                "protocolVersion", "2025-06-18",
                "clientInfo", Map.of("name", "Test Client", "version", "1.0.0")
        ));

        // 执行测试
        StepVerifier.create(mcpServer.handleMessage(request))
                .assertNext(response -> {
                    assertNotNull(response);
                    assertEquals(1, response.getId());
                    assertNull(response.getError());
                    
                    @SuppressWarnings("unchecked")
                    Map<String, Object> result = (Map<String, Object>) response.getResult();
                    assertNotNull(result);
                    assertEquals("2025-06-18", result.get("protocolVersion"));
                    
                    @SuppressWarnings("unchecked")
                    Map<String, Object> serverInfo = (Map<String, Object>) result.get("serverInfo");
                    assertEquals("Test MCP Server", serverInfo.get("name"));
                    assertEquals("1.0.0-test", serverInfo.get("version"));
                })
                .verifyComplete();
    }

    @Test
    void testToolsList() {
        // 准备请求
        JsonRpcMessage request = new JsonRpcMessage();
        request.setId(2);
        request.setMethod("tools/list");
        request.setParams(Map.of());

        // 执行测试
        StepVerifier.create(mcpServer.handleMessage(request))
                .assertNext(response -> {
                    assertNotNull(response);
                    assertEquals(2, response.getId());
                    assertNull(response.getError());
                    
                    @SuppressWarnings("unchecked")
                    Map<String, Object> result = (Map<String, Object>) response.getResult();
                    assertNotNull(result);
                    
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> tools = (List<Map<String, Object>>) result.get("tools");
                    assertNotNull(tools);
                    assertTrue(tools.size() > 0);
                    
                    // 验证包含预期的工具
                    List<String> toolNames = tools.stream()
                            .map(tool -> (String) tool.get("name"))
                            .toList();
                    assertTrue(toolNames.contains("search_pages"));
                    assertTrue(toolNames.contains("get_page_content"));
                    assertTrue(toolNames.contains("list_spaces"));
                })
                .verifyComplete();
    }

    @Test
    void testSearchPagesTool() {
        // Mock Confluence客户端响应
        PageList mockPageList = createMockPageList();
        when(confluenceClient.searchPages(anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(Mono.just(mockPageList));

        // 准备请求
        JsonRpcMessage request = new JsonRpcMessage();
        request.setId(3);
        request.setMethod("tools/call");
        request.setParams(Map.of(
                "name", "search_pages",
                "arguments", Map.of(
                        "query", "测试文档",
                        "limit", 5
                )
        ));

        // 执行测试
        StepVerifier.create(mcpServer.handleMessage(request))
                .assertNext(response -> {
                    assertNotNull(response);
                    assertEquals(3, response.getId());
                    assertNull(response.getError());
                    
                    @SuppressWarnings("unchecked")
                    Map<String, Object> result = (Map<String, Object>) response.getResult();
                    assertNotNull(result);
                    assertEquals(false, result.get("isError"));
                    
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> content = (List<Map<String, Object>>) result.get("content");
                    assertNotNull(content);
                    assertTrue(content.size() > 0);
                    
                    Map<String, Object> textContent = content.get(0);
                    assertEquals("text", textContent.get("type"));
                    assertTrue(((String) textContent.get("text")).contains("搜索结果"));
                })
                .verifyComplete();

        // 验证调用
        verify(confluenceClient).searchPages("测试文档", null, 0, 5);
    }

    @Test
    void testGetPageContentTool() {
        // Mock Confluence客户端响应
        Page mockPage = createMockPage();
        when(confluenceClient.getPage(anyString(), anyString()))
                .thenReturn(Mono.just(mockPage));

        // 准备请求
        JsonRpcMessage request = new JsonRpcMessage();
        request.setId(4);
        request.setMethod("tools/call");
        request.setParams(Map.of(
                "name", "get_page_content",
                "arguments", Map.of("pageId", "123456")
        ));

        // 执行测试
        StepVerifier.create(mcpServer.handleMessage(request))
                .assertNext(response -> {
                    assertNotNull(response);
                    assertEquals(4, response.getId());
                    assertNull(response.getError());
                    
                    @SuppressWarnings("unchecked")
                    Map<String, Object> result = (Map<String, Object>) response.getResult();
                    assertNotNull(result);
                    assertEquals(false, result.get("isError"));
                    
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> content = (List<Map<String, Object>>) result.get("content");
                    assertNotNull(content);
                    assertTrue(content.size() > 0);
                    
                    String textContent = (String) content.get(0).get("text");
                    assertTrue(textContent.contains("测试页面"));
                    assertTrue(textContent.contains("123456"));
                })
                .verifyComplete();

        verify(confluenceClient).getPage("123456", "body.storage,body.view,space,version,history");
    }

    @Test
    void testResourcesList() {
        // Mock Confluence客户端响应
        List<Space> mockSpaces = List.of(createMockSpace());
        when(confluenceClient.getAllSpaces(anyInt(), anyInt()))
                .thenReturn(Mono.just(mockSpaces));

        // 准备请求
        JsonRpcMessage request = new JsonRpcMessage();
        request.setId(5);
        request.setMethod("resources/list");
        request.setParams(Map.of());

        // 执行测试
        StepVerifier.create(mcpServer.handleMessage(request))
                .assertNext(response -> {
                    assertNotNull(response);
                    assertEquals(5, response.getId());
                    assertNull(response.getError());
                    
                    @SuppressWarnings("unchecked")
                    Map<String, Object> result = (Map<String, Object>) response.getResult();
                    assertNotNull(result);
                    
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> resources = (List<Map<String, Object>>) result.get("resources");
                    assertNotNull(resources);
                    assertTrue(resources.size() > 0);
                    
                    // 验证资源格式
                    Map<String, Object> resource = resources.get(0);
                    assertNotNull(resource.get("uri"));
                    assertNotNull(resource.get("name"));
                    assertNotNull(resource.get("title"));
                })
                .verifyComplete();

        verify(confluenceClient).getAllSpaces(0, 50);
    }

    @Test
    void testUnknownMethod() {
        // 准备请求
        JsonRpcMessage request = new JsonRpcMessage();
        request.setId(6);
        request.setMethod("unknown/method");
        request.setParams(Map.of());

        // 执行测试
        StepVerifier.create(mcpServer.handleMessage(request))
                .assertNext(response -> {
                    assertNotNull(response);
                    assertEquals(6, response.getId());
                    assertNotNull(response.getError());
                    assertEquals(-32601, response.getError().getCode());
                    assertTrue(response.getError().getMessage().contains("方法未找到"));
                })
                .verifyComplete();
    }

    // 辅助方法：创建模拟数据
    private PageList createMockPageList() {
        PageList pageList = new PageList();
        pageList.setResults(List.of(createMockPage()));
        pageList.setSize(1);
        pageList.setStart(0);
        pageList.setLimit(10);
        return pageList;
    }

    private Page createMockPage() {
        Page page = new Page();
        page.setId("123456");
        page.setTitle("测试页面");
        page.setType("page");
        
        Page.Body body = new Page.Body();
        Page.Content viewContent = new Page.Content();
        viewContent.setValue("<p>这是一个测试页面的内容</p>");
        viewContent.setRepresentation("view");
        body.setView(viewContent);
        page.setBody(body);
        
        Page.Version version = new Page.Version();
        version.setNumber(1);
        version.setWhen("2024-01-01T10:00:00.000Z");
        page.setVersion(version);
        
        return page;
    }

    private Space createMockSpace() {
        Space space = new Space();
        space.setId("1");
        space.setKey("TEST");
        space.setName("测试空间");
        space.setType("global");
        return space;
    }
}
