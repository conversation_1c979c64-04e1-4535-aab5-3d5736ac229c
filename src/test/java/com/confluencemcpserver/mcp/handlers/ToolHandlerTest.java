package com.confluencemcpserver.mcp.handlers;

import com.confluencemcpserver.confluence.ConfluenceClient;
import com.confluencemcpserver.dto.McpTool;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ToolHandler测试类
 */
class ToolHandlerTest {

    @Mock
    private ConfluenceClient confluenceClient;

    private ToolHandler toolHandler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        toolHandler = new ToolHandler(confluenceClient);
    }

    @Test
    void testListToolsWithoutPagination() {
        List<McpTool> tools = toolHandler.listTools();
        
        assertNotNull(tools);
        assertEquals(7, tools.size()); // 应该有7个工具
        
        // 验证工具名称
        List<String> toolNames = tools.stream().map(McpTool::getName).toList();
        assertTrue(toolNames.contains("search_pages"));
        assertTrue(toolNames.contains("get_page_content"));
        assertTrue(toolNames.contains("list_attachments"));
        assertTrue(toolNames.contains("get_page_links"));
        assertTrue(toolNames.contains("list_spaces"));
        assertTrue(toolNames.contains("get_comments"));
        assertTrue(toolNames.contains("test_connection"));
    }

    @Test
    void testListToolsWithPagination() {
        // 测试第一页
        Map<String, Object> result = toolHandler.listTools(null);
        
        assertNotNull(result);
        assertTrue(result.containsKey("tools"));
        
        @SuppressWarnings("unchecked")
        List<McpTool> tools = (List<McpTool>) result.get("tools");
        assertNotNull(tools);
        assertTrue(tools.size() <= 10); // 页面大小限制
    }

    @Test
    void testListToolsWithCursor() {
        // 测试使用游标
        Map<String, Object> result = toolHandler.listTools("0");
        
        assertNotNull(result);
        assertTrue(result.containsKey("tools"));
        
        @SuppressWarnings("unchecked")
        List<McpTool> tools = (List<McpTool>) result.get("tools");
        assertNotNull(tools);
    }

    @Test
    void testToolSchemaValidation() {
        List<McpTool> tools = toolHandler.listTools();
        
        for (McpTool tool : tools) {
            // 验证基本字段
            assertNotNull(tool.getName());
            assertNotNull(tool.getTitle());
            assertNotNull(tool.getDescription());
            assertNotNull(tool.getInputSchema());
            
            // 验证新增字段
            assertNotNull(tool.getOutputSchema(), "工具 " + tool.getName() + " 应该有输出模式");
            assertNotNull(tool.getAnnotations(), "工具 " + tool.getName() + " 应该有注解");
            
            // 验证输入模式结构
            Map<String, Object> inputSchema = tool.getInputSchema();
            assertEquals("object", inputSchema.get("type"));
            assertTrue(inputSchema.containsKey("properties"));
            
            // 验证输出模式结构
            Map<String, Object> outputSchema = tool.getOutputSchema();
            assertEquals("object", outputSchema.get("type"));
            assertTrue(outputSchema.containsKey("properties"));
            
            // 验证注解结构
            Map<String, Object> annotations = tool.getAnnotations();
            assertTrue(annotations.containsKey("audience"));
            assertTrue(annotations.containsKey("priority"));
            assertTrue(annotations.containsKey("category"));
        }
    }

    @Test
    void testSearchPagesToolSchema() {
        List<McpTool> tools = toolHandler.listTools();
        McpTool searchTool = tools.stream()
                .filter(tool -> "search_pages".equals(tool.getName()))
                .findFirst()
                .orElse(null);
        
        assertNotNull(searchTool);
        assertEquals("🔍 搜索页面", searchTool.getTitle());
        assertEquals("在Confluence中搜索页面", searchTool.getDescription());
        
        // 验证输入模式
        Map<String, Object> inputSchema = searchTool.getInputSchema();
        @SuppressWarnings("unchecked")
        Map<String, Object> properties = (Map<String, Object>) inputSchema.get("properties");
        assertTrue(properties.containsKey("query"));
        assertTrue(properties.containsKey("spaceKey"));
        assertTrue(properties.containsKey("limit"));
        
        // 验证注解
        Map<String, Object> annotations = searchTool.getAnnotations();
        assertEquals("search", annotations.get("category"));
        assertEquals(0.8, annotations.get("priority"));
    }

    @Test
    void testTestConnectionToolSchema() {
        List<McpTool> tools = toolHandler.listTools();
        McpTool testTool = tools.stream()
                .filter(tool -> "test_connection".equals(tool.getName()))
                .findFirst()
                .orElse(null);
        
        assertNotNull(testTool);
        assertEquals("🧪 测试连接", testTool.getTitle());
        
        // 验证注解
        Map<String, Object> annotations = testTool.getAnnotations();
        assertEquals("testing", annotations.get("category"));
        assertEquals(1.0, annotations.get("priority"));
    }

    @Test
    void testNotifyToolsListChanged() {
        // 这个方法应该不抛出异常
        assertDoesNotThrow(() -> toolHandler.notifyToolsListChanged());
    }
}
