package com.confluencemcpserver;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest
@TestPropertySource(properties = {
    "confluence.base-url=http://localhost:8080/confluence",
    "confluence.username=test",
    "confluence.password=test"
})
class ConfluenceMcpServerApplicationTests {

    @Test
    void contextLoads() {
        // 测试Spring上下文是否能正常加载
    }
}
