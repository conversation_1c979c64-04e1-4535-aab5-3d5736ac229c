package com.confluencemcpserver;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;
import java.util.Map;
import java.util.Scanner;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * MCP测试客户端
 * 用于手动测试MCP服务器的功能
 */
public class TestClient {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static WebSocketClient client;
    private static int messageId = 1;
    private static final CountDownLatch connectionLatch = new CountDownLatch(1);

    public static void main(String[] args) {
        System.out.println("=== Confluence MCP 测试客户端 ===");
        System.out.println("正在连接到 ws://localhost:8080/mcp ...");

        try {
            URI serverUri = new URI("ws://localhost:8080/mcp");
            
            client = new WebSocketClient(serverUri) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    System.out.println("✅ WebSocket连接已建立");
                    connectionLatch.countDown();
                    
                    // 发送初始化消息
                    sendInitialize();
                }

                @Override
                public void onMessage(String message) {
                    System.out.println("📨 收到消息:");
                    try {
                        Object jsonObject = objectMapper.readValue(message, Object.class);
                        String prettyJson = objectMapper.writerWithDefaultPrettyPrinter()
                                .writeValueAsString(jsonObject);
                        System.out.println(prettyJson);
                    } catch (Exception e) {
                        System.out.println(message);
                    }
                    System.out.println();
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    System.out.println("❌ WebSocket连接已关闭: " + reason);
                }

                @Override
                public void onError(Exception ex) {
                    System.err.println("❌ WebSocket错误: " + ex.getMessage());
                }
            };

            client.connect();

            // 等待连接建立
            if (!connectionLatch.await(10, TimeUnit.SECONDS)) {
                System.err.println("❌ 连接超时");
                return;
            }

            // 启动交互式命令行
            startInteractiveMode();

        } catch (Exception e) {
            System.err.println("❌ 连接失败: " + e.getMessage());
        }
    }

    private static void sendInitialize() {
        Map<String, Object> message = Map.of(
                "jsonrpc", "2.0",
                "id", messageId++,
                "method", "initialize",
                "params", Map.of(
                        "protocolVersion", "2025-06-18",
                        "clientInfo", Map.of(
                                "name", "Confluence MCP Test Client",
                                "version", "1.0.0"
                        )
                )
        );
        sendMessage(message);
    }

    private static void startInteractiveMode() {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("🎯 进入交互模式，输入命令进行测试:");
        System.out.println("命令列表:");
        System.out.println("  1 - 列出工具");
        System.out.println("  2 - 搜索页面");
        System.out.println("  3 - 获取页面内容");
        System.out.println("  4 - 列出空间");
        System.out.println("  5 - 列出资源");
        System.out.println("  6 - 列出提示");
        System.out.println("  7 - 获取提示");
        System.out.println("  q - 退出");
        System.out.println();

        while (true) {
            System.out.print("请输入命令: ");
            String input = scanner.nextLine().trim();

            switch (input) {
                case "1":
                    listTools();
                    break;
                case "2":
                    searchPages(scanner);
                    break;
                case "3":
                    getPageContent(scanner);
                    break;
                case "4":
                    listSpaces();
                    break;
                case "5":
                    listResources();
                    break;
                case "6":
                    listPrompts();
                    break;
                case "7":
                    getPrompt(scanner);
                    break;
                case "q":
                    System.out.println("👋 再见!");
                    client.close();
                    System.exit(0);
                    break;
                default:
                    System.out.println("❌ 未知命令: " + input);
            }
        }
    }

    private static void listTools() {
        Map<String, Object> message = Map.of(
                "jsonrpc", "2.0",
                "id", messageId++,
                "method", "tools/list",
                "params", Map.of()
        );
        sendMessage(message);
    }

    private static void searchPages(Scanner scanner) {
        System.out.print("请输入搜索关键词: ");
        String query = scanner.nextLine().trim();
        
        System.out.print("请输入空间键 (可选，直接回车跳过): ");
        String spaceKey = scanner.nextLine().trim();

        Map<String, Object> arguments = Map.of(
                "query", query.isEmpty() ? "文档" : query,
                "limit", 5
        );

        if (!spaceKey.isEmpty()) {
            arguments = Map.of(
                    "query", query.isEmpty() ? "文档" : query,
                    "spaceKey", spaceKey,
                    "limit", 5
            );
        }

        Map<String, Object> message = Map.of(
                "jsonrpc", "2.0",
                "id", messageId++,
                "method", "tools/call",
                "params", Map.of(
                        "name", "search_pages",
                        "arguments", arguments
                )
        );
        sendMessage(message);
    }

    private static void getPageContent(Scanner scanner) {
        System.out.print("请输入页面ID: ");
        String pageId = scanner.nextLine().trim();

        if (pageId.isEmpty()) {
            System.out.println("❌ 页面ID不能为空");
            return;
        }

        Map<String, Object> message = Map.of(
                "jsonrpc", "2.0",
                "id", messageId++,
                "method", "tools/call",
                "params", Map.of(
                        "name", "get_page_content",
                        "arguments", Map.of("pageId", pageId)
                )
        );
        sendMessage(message);
    }

    private static void listSpaces() {
        Map<String, Object> message = Map.of(
                "jsonrpc", "2.0",
                "id", messageId++,
                "method", "tools/call",
                "params", Map.of(
                        "name", "list_spaces",
                        "arguments", Map.of("limit", 10)
                )
        );
        sendMessage(message);
    }

    private static void listResources() {
        Map<String, Object> message = Map.of(
                "jsonrpc", "2.0",
                "id", messageId++,
                "method", "resources/list",
                "params", Map.of()
        );
        sendMessage(message);
    }

    private static void listPrompts() {
        Map<String, Object> message = Map.of(
                "jsonrpc", "2.0",
                "id", messageId++,
                "method", "prompts/list",
                "params", Map.of()
        );
        sendMessage(message);
    }

    private static void getPrompt(Scanner scanner) {
        System.out.print("请输入提示名称 (analyze_page/summarize_space/compare_pages/extract_key_info): ");
        String promptName = scanner.nextLine().trim();

        if (promptName.isEmpty()) {
            promptName = "analyze_page";
        }

        Map<String, Object> arguments = Map.of();

        if ("analyze_page".equals(promptName) || "extract_key_info".equals(promptName)) {
            System.out.print("请输入页面ID: ");
            String pageId = scanner.nextLine().trim();
            if (!pageId.isEmpty()) {
                arguments = Map.of("pageId", pageId);
            }
        } else if ("summarize_space".equals(promptName)) {
            System.out.print("请输入空间键: ");
            String spaceKey = scanner.nextLine().trim();
            if (!spaceKey.isEmpty()) {
                arguments = Map.of("spaceKey", spaceKey);
            }
        }

        Map<String, Object> message = Map.of(
                "jsonrpc", "2.0",
                "id", messageId++,
                "method", "prompts/get",
                "params", Map.of(
                        "name", promptName,
                        "arguments", arguments
                )
        );
        sendMessage(message);
    }

    private static void sendMessage(Map<String, Object> message) {
        try {
            String json = objectMapper.writeValueAsString(message);
            System.out.println("📤 发送消息: " + message.get("method"));
            client.send(json);
        } catch (Exception e) {
            System.err.println("❌ 发送消息失败: " + e.getMessage());
        }
    }
}
