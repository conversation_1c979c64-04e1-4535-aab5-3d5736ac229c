package com.confluencemcpserver;

import com.confluencemcpserver.mcp.transport.StdioTransport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * Confluence MCP服务器 - STDIO模式
 * 
 * 这是一个符合MCP标准的STDIO模式服务器，用于与Augment等MCP客户端通信。
 */
@SpringBootApplication
@EnableConfigurationProperties
public class McpStdioApplication {
    
    private static final Logger logger = LoggerFactory.getLogger(McpStdioApplication.class);

    public static void main(String[] args) {
        // 设置系统属性，禁用Spring Boot的banner和web环境
        System.setProperty("spring.main.banner-mode", "off");
        System.setProperty("spring.main.web-application-type", "none");
        System.setProperty("logging.config", "classpath:logback-stdio.xml");
        
        logger.info("启动Confluence MCP服务器 (STDIO模式)...");
        
        SpringApplication app = new SpringApplication(McpStdioApplication.class);
        app.setWebApplicationType(org.springframework.boot.WebApplicationType.NONE);
        app.run(args);
    }

    /**
     * 启动STDIO传输层
     */
    @Bean
    public CommandLineRunner stdioRunner(StdioTransport stdioTransport) {
        return args -> {
            logger.info("MCP服务器已准备就绪，等待STDIO输入...");
            stdioTransport.start();
        };
    }
}
