package com.confluencemcpserver.config;

import com.confluencemcpserver.mcp.transport.WebSocketTransport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket配置
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    
    private final WebSocketTransport webSocketTransport;
    private final McpConfig mcpConfig;

    @Autowired
    public WebSocketConfig(WebSocketTransport webSocketTransport, McpConfig mcpConfig) {
        this.webSocketTransport = webSocketTransport;
        this.mcpConfig = mcpConfig;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(webSocketTransport, mcpConfig.getWebsocketPath())
                .setAllowedOrigins("*"); // 在生产环境中应该限制允许的源
    }
}
