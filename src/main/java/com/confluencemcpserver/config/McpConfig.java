package com.confluencemcpserver.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * MCP服务器配置
 */
@Configuration
@ConfigurationProperties(prefix = "mcp")
public class McpConfig {
    
    /**
     * MCP服务器名称
     */
    private String serverName = "Confluence MCP Server";
    
    /**
     * MCP服务器版本
     */
    private String serverVersion = "1.0.0";
    
    /**
     * WebSocket端口
     */
    private int websocketPort = 8081;
    
    /**
     * WebSocket路径
     */
    private String websocketPath = "/mcp";
    
    /**
     * 是否启用资源功能
     */
    private boolean resourcesEnabled = true;
    
    /**
     * 是否启用工具功能
     */
    private boolean toolsEnabled = true;
    
    /**
     * 是否启用提示功能
     */
    private boolean promptsEnabled = true;
    
    /**
     * 是否启用资源订阅
     */
    private boolean resourceSubscriptionEnabled = true;
    
    /**
     * 是否启用资源列表变更通知
     */
    private boolean resourceListChangedEnabled = true;

    /**
     * 是否启用工具列表变更通知
     */
    private boolean toolsListChangedEnabled = true;

    // Getters and Setters
    public String getServerName() {
        return serverName;
    }

    public void setServerName(String serverName) {
        this.serverName = serverName;
    }

    public String getServerVersion() {
        return serverVersion;
    }

    public void setServerVersion(String serverVersion) {
        this.serverVersion = serverVersion;
    }

    public int getWebsocketPort() {
        return websocketPort;
    }

    public void setWebsocketPort(int websocketPort) {
        this.websocketPort = websocketPort;
    }

    public String getWebsocketPath() {
        return websocketPath;
    }

    public void setWebsocketPath(String websocketPath) {
        this.websocketPath = websocketPath;
    }

    public boolean isResourcesEnabled() {
        return resourcesEnabled;
    }

    public void setResourcesEnabled(boolean resourcesEnabled) {
        this.resourcesEnabled = resourcesEnabled;
    }

    public boolean isToolsEnabled() {
        return toolsEnabled;
    }

    public void setToolsEnabled(boolean toolsEnabled) {
        this.toolsEnabled = toolsEnabled;
    }

    public boolean isPromptsEnabled() {
        return promptsEnabled;
    }

    public void setPromptsEnabled(boolean promptsEnabled) {
        this.promptsEnabled = promptsEnabled;
    }

    public boolean isResourceSubscriptionEnabled() {
        return resourceSubscriptionEnabled;
    }

    public void setResourceSubscriptionEnabled(boolean resourceSubscriptionEnabled) {
        this.resourceSubscriptionEnabled = resourceSubscriptionEnabled;
    }

    public boolean isResourceListChangedEnabled() {
        return resourceListChangedEnabled;
    }

    public void setResourceListChangedEnabled(boolean resourceListChangedEnabled) {
        this.resourceListChangedEnabled = resourceListChangedEnabled;
    }

    public boolean isToolsListChangedEnabled() {
        return toolsListChangedEnabled;
    }

    public void setToolsListChangedEnabled(boolean toolsListChangedEnabled) {
        this.toolsListChangedEnabled = toolsListChangedEnabled;
    }
}
