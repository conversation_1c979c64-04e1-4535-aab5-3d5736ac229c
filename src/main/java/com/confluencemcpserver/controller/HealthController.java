package com.confluencemcpserver.controller;

import com.confluencemcpserver.config.ConfluenceConfig;
import com.confluencemcpserver.config.McpConfig;
import com.confluencemcpserver.confluence.ConfluenceClient;
import com.confluencemcpserver.mcp.transport.WebSocketTransport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 */
@RestController
@RequestMapping("/api")
public class HealthController {
    
    private final ConfluenceConfig confluenceConfig;
    private final McpConfig mcpConfig;
    private final WebSocketTransport webSocketTransport;
    private final ConfluenceClient confluenceClient;

    @Autowired
    public HealthController(ConfluenceConfig confluenceConfig, McpConfig mcpConfig,
                           WebSocketTransport webSocketTransport, ConfluenceClient confluenceClient) {
        this.confluenceConfig = confluenceConfig;
        this.mcpConfig = mcpConfig;
        this.webSocketTransport = webSocketTransport;
        this.confluenceClient = confluenceClient;
    }

    /**
     * 健康检查端点
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", Instant.now().toString());
        health.put("server", Map.of(
                "name", mcpConfig.getServerName(),
                "version", mcpConfig.getServerVersion()
        ));
        
        return ResponseEntity.ok(health);
    }

    /**
     * 服务状态端点
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> status() {
        Map<String, Object> status = new HashMap<>();
        
        // 基本信息
        status.put("server", Map.of(
                "name", mcpConfig.getServerName(),
                "version", mcpConfig.getServerVersion(),
                "timestamp", Instant.now().toString()
        ));
        
        // Confluence配置（隐藏敏感信息）
        status.put("confluence", Map.of(
                "baseUrl", confluenceConfig.getBaseUrl(),
                "username", confluenceConfig.getUsername(),
                "connectTimeout", confluenceConfig.getConnectTimeout(),
                "readTimeout", confluenceConfig.getReadTimeout(),
                "sslVerification", confluenceConfig.isSslVerification()
        ));
        
        // MCP配置
        status.put("mcp", Map.of(
                "websocketPath", mcpConfig.getWebsocketPath(),
                "resourcesEnabled", mcpConfig.isResourcesEnabled(),
                "toolsEnabled", mcpConfig.isToolsEnabled(),
                "promptsEnabled", mcpConfig.isPromptsEnabled(),
                "resourceSubscriptionEnabled", mcpConfig.isResourceSubscriptionEnabled(),
                "resourceListChangedEnabled", mcpConfig.isResourceListChangedEnabled()
        ));
        
        // WebSocket连接状态
        status.put("websocket", Map.of(
                "activeSessions", webSocketTransport.getActiveSessionCount(),
                "endpoint", "ws://localhost:8080" + mcpConfig.getWebsocketPath()
        ));
        
        return ResponseEntity.ok(status);
    }

    /**
     * 配置信息端点
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> info() {
        Map<String, Object> info = new HashMap<>();
        
        info.put("application", Map.of(
                "name", "Confluence MCP Server",
                "description", "基于Model Context Protocol的Confluence集成服务器",
                "version", mcpConfig.getServerVersion()
        ));
        
        info.put("features", Map.of(
                "resources", "提供Confluence页面、附件、空间等资源访问",
                "tools", "提供搜索、获取内容、列出附件等工具",
                "prompts", "提供页面分析、空间总结等提示模板"
        ));
        
        info.put("endpoints", Map.of(
                "websocket", "ws://localhost:8080" + mcpConfig.getWebsocketPath(),
                "health", "/api/health",
                "status", "/api/status",
                "info", "/api/info"
        ));
        
        info.put("documentation", Map.of(
                "mcp", "https://modelcontextprotocol.io/",
                "confluence", "https://developer.atlassian.com/server/confluence/",
                "github", "https://github.com/your-repo/confluence-mcp-server"
        ));
        
        return ResponseEntity.ok(info);
    }
}
