package com.confluencemcpserver.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * MCP提示定义
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class McpPrompt {
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("arguments")
    private List<Argument> arguments;

    public McpPrompt() {}

    public McpPrompt(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public McpPrompt(String name, String title, String description, List<Argument> arguments) {
        this.name = name;
        this.title = title;
        this.description = description;
        this.arguments = arguments;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<Argument> getArguments() {
        return arguments;
    }

    public void setArguments(List<Argument> arguments) {
        this.arguments = arguments;
    }

    /**
     * 提示参数
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Argument {
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("required")
        private Boolean required;

        public Argument() {}

        public Argument(String name, String description, Boolean required) {
            this.name = name;
            this.description = description;
            this.required = required;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public Boolean getRequired() {
            return required;
        }

        public void setRequired(Boolean required) {
            this.required = required;
        }
    }
}
