package com.confluencemcpserver.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * MCP资源定义
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class McpResource {
    
    @JsonProperty("uri")
    private String uri;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("mimeType")
    private String mimeType;
    
    @JsonProperty("size")
    private Long size;
    
    @JsonProperty("annotations")
    private Annotations annotations;

    public McpResource() {}

    public McpResource(String uri, String name) {
        this.uri = uri;
        this.name = name;
    }

    public McpResource(String uri, String name, String title, String description, String mimeType) {
        this.uri = uri;
        this.name = name;
        this.title = title;
        this.description = description;
        this.mimeType = mimeType;
    }

    // Getters and Setters
    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Annotations getAnnotations() {
        return annotations;
    }

    public void setAnnotations(Annotations annotations) {
        this.annotations = annotations;
    }

    /**
     * 资源注解
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Annotations {
        @JsonProperty("audience")
        private String[] audience;
        
        @JsonProperty("priority")
        private Double priority;
        
        @JsonProperty("lastModified")
        private String lastModified;

        public Annotations() {}

        public Annotations(String[] audience, Double priority) {
            this.audience = audience;
            this.priority = priority;
        }

        public String[] getAudience() {
            return audience;
        }

        public void setAudience(String[] audience) {
            this.audience = audience;
        }

        public Double getPriority() {
            return priority;
        }

        public void setPriority(Double priority) {
            this.priority = priority;
        }

        public String getLastModified() {
            return lastModified;
        }

        public void setLastModified(String lastModified) {
            this.lastModified = lastModified;
        }
    }
}
