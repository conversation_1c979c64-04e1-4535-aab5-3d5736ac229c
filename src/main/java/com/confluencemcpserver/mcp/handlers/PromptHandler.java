package com.confluencemcpserver.mcp.handlers;

import com.confluencemcpserver.confluence.ConfluenceClient;
import com.confluencemcpserver.dto.McpPrompt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.*;

/**
 * MCP提示处理器
 */
@Component
public class PromptHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(PromptHandler.class);
    
    private final ConfluenceClient confluenceClient;

    @Autowired
    public PromptHandler(ConfluenceClient confluenceClient) {
        this.confluenceClient = confluenceClient;
    }

    /**
     * 列出所有提示
     */
    public List<McpPrompt> listPrompts() {
        List<McpPrompt> prompts = new ArrayList<>();
        
        // 分析页面提示
        prompts.add(createAnalyzePagePrompt());
        
        // 总结空间提示
        prompts.add(createSummarizeSpacePrompt());
        
        // 比较页面提示
        prompts.add(createComparePagesPrompt());
        
        // 提取关键信息提示
        prompts.add(createExtractKeyInfoPrompt());
        
        logger.debug("列出 {} 个提示", prompts.size());
        return prompts;
    }

    /**
     * 获取提示内容
     */
    public Mono<Map<String, Object>> getPrompt(String name, Map<String, Object> arguments) {
        logger.debug("获取提示: {} 参数: {}", name, arguments);
        
        switch (name) {
            case "analyze_page":
                return getAnalyzePagePrompt(arguments);
            case "summarize_space":
                return getSummarizeSpacePrompt(arguments);
            case "compare_pages":
                return getComparePagesPrompt(arguments);
            case "extract_key_info":
                return getExtractKeyInfoPrompt(arguments);
            default:
                return Mono.error(new IllegalArgumentException("未知提示: " + name));
        }
    }

    /**
     * 创建分析页面提示
     */
    private McpPrompt createAnalyzePagePrompt() {
        List<McpPrompt.Argument> arguments = Arrays.asList(
                new McpPrompt.Argument("pageId", "要分析的页面ID", true),
                new McpPrompt.Argument("focus", "分析重点（可选）", false)
        );
        
        return new McpPrompt("analyze_page", "📊 分析页面", 
                "深入分析Confluence页面的内容、结构和关键信息", arguments);
    }

    /**
     * 创建总结空间提示
     */
    private McpPrompt createSummarizeSpacePrompt() {
        List<McpPrompt.Argument> arguments = Arrays.asList(
                new McpPrompt.Argument("spaceKey", "要总结的空间键", true),
                new McpPrompt.Argument("includePages", "是否包含页面详情", false)
        );
        
        return new McpPrompt("summarize_space", "📋 总结空间", 
                "总结Confluence空间的整体内容和结构", arguments);
    }

    /**
     * 创建比较页面提示
     */
    private McpPrompt createComparePagesPrompt() {
        List<McpPrompt.Argument> arguments = Arrays.asList(
                new McpPrompt.Argument("pageId1", "第一个页面ID", true),
                new McpPrompt.Argument("pageId2", "第二个页面ID", true),
                new McpPrompt.Argument("compareAspect", "比较维度（内容、结构、更新等）", false)
        );
        
        return new McpPrompt("compare_pages", "🔄 比较页面", 
                "比较两个Confluence页面的差异和相似性", arguments);
    }

    /**
     * 创建提取关键信息提示
     */
    private McpPrompt createExtractKeyInfoPrompt() {
        List<McpPrompt.Argument> arguments = Arrays.asList(
                new McpPrompt.Argument("pageId", "页面ID", true),
                new McpPrompt.Argument("infoType", "信息类型（联系人、日期、任务等）", false)
        );
        
        return new McpPrompt("extract_key_info", "🔍 提取关键信息", 
                "从Confluence页面中提取特定类型的关键信息", arguments);
    }

    /**
     * 获取分析页面提示内容
     */
    private Mono<Map<String, Object>> getAnalyzePagePrompt(Map<String, Object> arguments) {
        String pageId = (String) arguments.get("pageId");
        String focus = (String) arguments.getOrDefault("focus", "全面分析");
        
        return confluenceClient.getPage(pageId, "body.storage,body.view,space,version,history")
                .map(page -> {
                    String prompt = String.format("""
                            请分析以下Confluence页面，重点关注：%s
                            
                            页面信息：
                            - 标题：%s
                            - ID：%s
                            - 空间：%s
                            - 版本：%s
                            - 最后修改：%s
                            
                            页面内容：
                            %s
                            
                            请从以下角度进行分析：
                            1. 内容结构和组织
                            2. 关键信息和要点
                            3. 文档质量和完整性
                            4. 可能的改进建议
                            5. 与其他页面的关联性
                            """,
                            focus,
                            page.getTitle(),
                            page.getId(),
                            page.getSpace() != null ? page.getSpace().getName() : "未知",
                            page.getVersion() != null ? page.getVersion().getNumber() : "未知",
                            page.getVersion() != null ? page.getVersion().getWhen() : "未知",
                            page.getBody() != null && page.getBody().getView() != null 
                                    ? page.getBody().getView().getValue() 
                                    : "无内容"
                    );
                    
                    return createPromptResult(prompt);
                })
                .onErrorReturn(createErrorPromptResult("获取页面信息失败"));
    }

    /**
     * 获取总结空间提示内容
     */
    private Mono<Map<String, Object>> getSummarizeSpacePrompt(Map<String, Object> arguments) {
        String spaceKey = (String) arguments.get("spaceKey");
        Boolean includePages = (Boolean) arguments.getOrDefault("includePages", false);
        
        return confluenceClient.getSpace(spaceKey)
                .map(space -> {
                    String prompt = String.format("""
                            请总结以下Confluence空间的内容：
                            
                            空间信息：
                            - 名称：%s
                            - 键：%s
                            - 类型：%s
                            - 描述：%s
                            - 创建时间：%s
                            
                            %s
                            
                            请提供以下总结：
                            1. 空间的主要用途和目标
                            2. 内容组织结构
                            3. 关键页面和文档
                            4. 活跃度和维护状态
                            5. 改进建议
                            """,
                            space.getName(),
                            space.getKey(),
                            space.getType(),
                            space.getDescription() != null && space.getDescription().getPlain() != null 
                                    ? space.getDescription().getPlain().getValue() : "无描述",
                            space.getCreationDate() != null ? space.getCreationDate() : "未知",
                            includePages ? "注意：需要进一步获取空间内的页面列表进行详细分析。" : ""
                    );
                    
                    return createPromptResult(prompt);
                })
                .onErrorReturn(createErrorPromptResult("获取空间信息失败"));
    }

    /**
     * 获取比较页面提示内容
     */
    private Mono<Map<String, Object>> getComparePagesPrompt(Map<String, Object> arguments) {
        String pageId1 = (String) arguments.get("pageId1");
        String pageId2 = (String) arguments.get("pageId2");
        String compareAspect = (String) arguments.getOrDefault("compareAspect", "全面比较");
        
        Mono<String> page1Info = confluenceClient.getPage(pageId1, "body.view,space,version")
                .map(this::formatPageForComparison)
                .onErrorReturn("页面1获取失败");
        
        Mono<String> page2Info = confluenceClient.getPage(pageId2, "body.view,space,version")
                .map(this::formatPageForComparison)
                .onErrorReturn("页面2获取失败");
        
        return Mono.zip(page1Info, page2Info)
                .map(tuple -> {
                    String prompt = String.format("""
                            请比较以下两个Confluence页面，重点关注：%s
                            
                            页面1信息：
                            %s
                            
                            页面2信息：
                            %s
                            
                            请从以下角度进行比较：
                            1. 内容相似性和差异
                            2. 结构和组织方式
                            3. 信息完整性
                            4. 更新频率和时效性
                            5. 用途和目标受众
                            6. 建议的整合或改进方案
                            """,
                            compareAspect,
                            tuple.getT1(),
                            tuple.getT2()
                    );
                    
                    return createPromptResult(prompt);
                });
    }

    /**
     * 获取提取关键信息提示内容
     */
    private Mono<Map<String, Object>> getExtractKeyInfoPrompt(Map<String, Object> arguments) {
        String pageId = (String) arguments.get("pageId");
        String infoType = (String) arguments.getOrDefault("infoType", "所有关键信息");
        
        return confluenceClient.getPage(pageId, "body.view,space")
                .map(page -> {
                    String prompt = String.format("""
                            请从以下Confluence页面中提取关键信息，重点关注：%s
                            
                            页面信息：
                            - 标题：%s
                            - 空间：%s
                            
                            页面内容：
                            %s
                            
                            请提取以下类型的信息：
                            1. 联系人信息（姓名、邮箱、电话等）
                            2. 重要日期和时间
                            3. 任务和待办事项
                            4. 关键数据和指标
                            5. 链接和引用
                            6. 决策和结论
                            7. 风险和问题
                            
                            请以结构化的方式组织提取的信息。
                            """,
                            infoType,
                            page.getTitle(),
                            page.getSpace() != null ? page.getSpace().getName() : "未知",
                            page.getBody() != null && page.getBody().getView() != null 
                                    ? page.getBody().getView().getValue() 
                                    : "无内容"
                    );
                    
                    return createPromptResult(prompt);
                })
                .onErrorReturn(createErrorPromptResult("获取页面信息失败"));
    }

    /**
     * 格式化页面信息用于比较
     */
    private String formatPageForComparison(com.confluencemcpserver.confluence.model.Page page) {
        StringBuilder info = new StringBuilder();
        info.append("标题：").append(page.getTitle()).append("\n");
        info.append("ID：").append(page.getId()).append("\n");
        
        if (page.getSpace() != null) {
            info.append("空间：").append(page.getSpace().getName()).append("\n");
        }
        
        if (page.getVersion() != null) {
            info.append("版本：").append(page.getVersion().getNumber()).append("\n");
            if (page.getVersion().getWhen() != null) {
                info.append("最后修改：").append(page.getVersion().getWhen()).append("\n");
            }
        }
        
        info.append("\n内容：\n");
        if (page.getBody() != null && page.getBody().getView() != null) {
            String content = page.getBody().getView().getValue();
            if (content.length() > 1000) {
                content = content.substring(0, 1000) + "...";
            }
            info.append(content);
        }
        
        return info.toString();
    }

    /**
     * 创建提示结果
     */
    private Map<String, Object> createPromptResult(String prompt) {
        Map<String, Object> result = new HashMap<>();
        result.put("messages", List.of(
                Map.of("role", "user", "content", Map.of("type", "text", "text", prompt))
        ));
        return result;
    }

    /**
     * 创建错误提示结果
     */
    private Map<String, Object> createErrorPromptResult(String error) {
        return createPromptResult("错误：" + error + "\n\n请检查参数是否正确，然后重试。");
    }
}
