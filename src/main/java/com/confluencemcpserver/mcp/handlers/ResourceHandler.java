package com.confluencemcpserver.mcp.handlers;

import com.confluencemcpserver.confluence.ConfluenceClient;
import com.confluencemcpserver.confluence.model.Attachment;
import com.confluencemcpserver.confluence.model.Page;
import com.confluencemcpserver.confluence.model.Space;
import com.confluencemcpserver.dto.McpResource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP资源处理器
 */
@Component
public class ResourceHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(ResourceHandler.class);
    
    private final ConfluenceClient confluenceClient;

    @Autowired
    public ResourceHandler(ConfluenceClient confluenceClient) {
        this.confluenceClient = confluenceClient;
    }

    /**
     * 列出所有资源
     */
    public Mono<List<McpResource>> listResources(String cursor) {
        return confluenceClient.getAllSpaces(0, 50)
                .flatMapMany(Flux::fromIterable)
                .flatMap(this::createSpaceResources)
                .collectList()
                .doOnSuccess(resources -> logger.debug("列出 {} 个资源", resources.size()));
    }

    /**
     * 读取资源内容
     */
    public Mono<Map<String, Object>> readResource(String uri) {
        logger.debug("读取资源: {}", uri);
        
        if (uri.startsWith("confluence://page/")) {
            String pageId = extractPageId(uri);
            return readPageResource(pageId);
        } else if (uri.startsWith("confluence://attachment/")) {
            String attachmentId = extractAttachmentId(uri);
            return readAttachmentResource(attachmentId);
        } else if (uri.startsWith("confluence://space/")) {
            String spaceKey = extractSpaceKey(uri);
            return readSpaceResource(spaceKey);
        } else {
            return Mono.error(new IllegalArgumentException("不支持的资源URI: " + uri));
        }
    }

    /**
     * 创建空间相关资源
     */
    private Flux<McpResource> createSpaceResources(Space space) {
        return Flux.just(
                // 空间资源
                createSpaceResource(space),
                // 空间首页资源
                createPageResource(space.getHomepage(), space)
        );
    }

    /**
     * 创建空间资源
     */
    private McpResource createSpaceResource(Space space) {
        McpResource resource = new McpResource();
        resource.setUri("confluence://space/" + space.getKey());
        resource.setName(space.getName());
        resource.setTitle("📁 " + space.getName());
        resource.setDescription(space.getDescription() != null && space.getDescription().getPlain() != null 
                ? space.getDescription().getPlain().getValue() 
                : "Confluence空间: " + space.getName());
        resource.setMimeType("application/json");
        
        // 设置注解
        McpResource.Annotations annotations = new McpResource.Annotations();
        annotations.setAudience(new String[]{"user", "assistant"});
        annotations.setPriority(0.8);
        resource.setAnnotations(annotations);
        
        return resource;
    }

    /**
     * 创建页面资源
     */
    private McpResource createPageResource(Page page, Space space) {
        if (page == null) return null;
        
        McpResource resource = new McpResource();
        resource.setUri("confluence://page/" + page.getId());
        resource.setName(page.getTitle());
        resource.setTitle("📄 " + page.getTitle());
        resource.setDescription("Confluence页面: " + page.getTitle() + 
                (space != null ? " (空间: " + space.getName() + ")" : ""));
        resource.setMimeType("text/html");
        
        // 设置注解
        McpResource.Annotations annotations = new McpResource.Annotations();
        annotations.setAudience(new String[]{"user", "assistant"});
        annotations.setPriority(0.9);
        if (page.getVersion() != null && page.getVersion().getWhen() != null) {
            annotations.setLastModified(page.getVersion().getWhen());
        }
        resource.setAnnotations(annotations);
        
        return resource;
    }

    /**
     * 读取页面资源
     */
    private Mono<Map<String, Object>> readPageResource(String pageId) {
        return confluenceClient.getPage(pageId, "body.storage,body.view,space,version,history")
                .map(page -> {
                    Map<String, Object> content = new HashMap<>();
                    content.put("uri", "confluence://page/" + pageId);
                    content.put("name", page.getTitle());
                    content.put("title", "📄 " + page.getTitle());
                    content.put("mimeType", "text/html");
                    
                    // 页面内容
                    if (page.getBody() != null && page.getBody().getView() != null) {
                        content.put("text", page.getBody().getView().getValue());
                    } else if (page.getBody() != null && page.getBody().getStorage() != null) {
                        content.put("text", page.getBody().getStorage().getValue());
                    }
                    
                    return content;
                });
    }

    /**
     * 读取附件资源
     */
    private Mono<Map<String, Object>> readAttachmentResource(String attachmentId) {
        // 这里需要实现附件读取逻辑
        Map<String, Object> content = new HashMap<>();
        content.put("uri", "confluence://attachment/" + attachmentId);
        content.put("name", "附件");
        content.put("title", "📎 附件");
        content.put("mimeType", "application/octet-stream");
        content.put("text", "附件内容需要通过下载链接获取");
        
        return Mono.just(content);
    }

    /**
     * 读取空间资源
     */
    private Mono<Map<String, Object>> readSpaceResource(String spaceKey) {
        return confluenceClient.getSpace(spaceKey)
                .map(space -> {
                    Map<String, Object> content = new HashMap<>();
                    content.put("uri", "confluence://space/" + spaceKey);
                    content.put("name", space.getName());
                    content.put("title", "📁 " + space.getName());
                    content.put("mimeType", "application/json");
                    
                    // 空间信息
                    StringBuilder spaceInfo = new StringBuilder();
                    spaceInfo.append("空间名称: ").append(space.getName()).append("\n");
                    spaceInfo.append("空间键: ").append(space.getKey()).append("\n");
                    spaceInfo.append("空间类型: ").append(space.getType()).append("\n");
                    if (space.getDescription() != null && space.getDescription().getPlain() != null) {
                        spaceInfo.append("描述: ").append(space.getDescription().getPlain().getValue()).append("\n");
                    }
                    if (space.getCreationDate() != null) {
                        spaceInfo.append("创建时间: ").append(space.getCreationDate()).append("\n");
                    }
                    
                    content.put("text", spaceInfo.toString());
                    
                    return content;
                });
    }

    /**
     * 从URI中提取页面ID
     */
    private String extractPageId(String uri) {
        return uri.substring("confluence://page/".length());
    }

    /**
     * 从URI中提取附件ID
     */
    private String extractAttachmentId(String uri) {
        return uri.substring("confluence://attachment/".length());
    }

    /**
     * 从URI中提取空间键
     */
    private String extractSpaceKey(String uri) {
        return uri.substring("confluence://space/".length());
    }
}
