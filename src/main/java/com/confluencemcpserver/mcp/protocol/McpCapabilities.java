package com.confluencemcpserver.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * MCP服务器能力声明
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class McpCapabilities {
    
    @JsonProperty("resources")
    private ResourcesCapability resources;
    
    @JsonProperty("tools")
    private ToolsCapability tools;
    
    @JsonProperty("prompts")
    private PromptsCapability prompts;

    public McpCapabilities() {}

    public McpCapabilities(boolean resourcesEnabled, boolean toolsEnabled, boolean promptsEnabled,
                          boolean resourceSubscription, boolean resourceListChanged, boolean toolsListChanged) {
        if (resourcesEnabled) {
            this.resources = new ResourcesCapability(resourceSubscription, resourceListChanged);
        }
        if (toolsEnabled) {
            this.tools = new ToolsCapability(toolsListChanged);
        }
        if (promptsEnabled) {
            this.prompts = new PromptsCapability(true);
        }
    }

    // Getters and Setters
    public ResourcesCapability getResources() {
        return resources;
    }

    public void setResources(ResourcesCapability resources) {
        this.resources = resources;
    }

    public ToolsCapability getTools() {
        return tools;
    }

    public void setTools(ToolsCapability tools) {
        this.tools = tools;
    }

    public PromptsCapability getPrompts() {
        return prompts;
    }

    public void setPrompts(PromptsCapability prompts) {
        this.prompts = prompts;
    }

    /**
     * 资源能力
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ResourcesCapability {
        @JsonProperty("subscribe")
        private Boolean subscribe;
        
        @JsonProperty("listChanged")
        private Boolean listChanged;

        public ResourcesCapability() {}

        public ResourcesCapability(Boolean subscribe, Boolean listChanged) {
            this.subscribe = subscribe;
            this.listChanged = listChanged;
        }

        public Boolean getSubscribe() {
            return subscribe;
        }

        public void setSubscribe(Boolean subscribe) {
            this.subscribe = subscribe;
        }

        public Boolean getListChanged() {
            return listChanged;
        }

        public void setListChanged(Boolean listChanged) {
            this.listChanged = listChanged;
        }
    }

    /**
     * 工具能力
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ToolsCapability {
        @JsonProperty("listChanged")
        private Boolean listChanged;

        public ToolsCapability() {}

        public ToolsCapability(Boolean listChanged) {
            this.listChanged = listChanged;
        }

        public Boolean getListChanged() {
            return listChanged;
        }

        public void setListChanged(Boolean listChanged) {
            this.listChanged = listChanged;
        }
    }

    /**
     * 提示能力
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PromptsCapability {
        @JsonProperty("listChanged")
        private Boolean listChanged;

        public PromptsCapability() {}

        public PromptsCapability(Boolean listChanged) {
            this.listChanged = listChanged;
        }

        public Boolean getListChanged() {
            return listChanged;
        }

        public void setListChanged(Boolean listChanged) {
            this.listChanged = listChanged;
        }
    }
}
