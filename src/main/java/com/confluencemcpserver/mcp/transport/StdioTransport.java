package com.confluencemcpserver.mcp.transport;

import com.confluencemcpserver.mcp.McpServer;
import com.confluencemcpserver.mcp.protocol.JsonRpcMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.util.concurrent.CompletableFuture;

/**
 * STDIO传输层 - 符合MCP标准
 */
@Component
public class StdioTransport {
    
    private static final Logger logger = LoggerFactory.getLogger(StdioTransport.class);
    
    private final McpServer mcpServer;
    private final ObjectMapper objectMapper;
    private final BufferedReader reader;
    private final PrintWriter writer;

    @Autowired
    public StdioTransport(McpServer mcpServer) {
        this.mcpServer = mcpServer;
        this.objectMapper = new ObjectMapper();
        this.reader = new BufferedReader(new InputStreamReader(System.in));
        this.writer = new PrintWriter(System.out, true);
        
        // 将日志输出重定向到stderr，避免干扰STDIO通信
        System.setProperty("logging.config", "classpath:logback-stdio.xml");
    }

    /**
     * 启动STDIO消息循环
     */
    public void start() {
        logger.info("启动MCP STDIO传输层...");
        
        try {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.trim().isEmpty()) {
                    continue;
                }
                
                try {
                    // 解析JSON-RPC消息
                    JsonRpcMessage request = objectMapper.readValue(line, JsonRpcMessage.class);
                    logger.debug("收到请求: {}", request.getMethod());
                    
                    // 处理消息 - 同步等待结果
                    mcpServer.handleMessage(request)
                            .doOnNext(this::sendResponse)
                            .doOnError(error -> {
                                logger.error("处理消息时发生错误", error);
                                sendErrorResponse(request.getId(), -32603, "内部错误: " + error.getMessage());
                            })
                            .block(); // 阻塞等待结果
                            
                } catch (Exception e) {
                    logger.error("解析消息时发生错误: {}", line, e);
                    sendErrorResponse(null, -32700, "解析错误: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            logger.error("STDIO传输层错误", e);
        }
    }

    /**
     * 发送响应
     */
    private void sendResponse(JsonRpcMessage response) {
        try {
            String json = objectMapper.writeValueAsString(response);
            writer.println(json);
            writer.flush();
            logger.debug("发送响应: {}", response.getMethod() != null ? response.getMethod() : "response");
            
        } catch (Exception e) {
            logger.error("发送响应失败", e);
        }
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(Object id, int code, String message) {
        try {
            JsonRpcMessage.JsonRpcError error = new JsonRpcMessage.JsonRpcError(code, message);
            JsonRpcMessage response = new JsonRpcMessage(id, error);
            
            String json = objectMapper.writeValueAsString(response);
            writer.println(json);
            writer.flush();
            
        } catch (Exception e) {
            logger.error("发送错误响应失败", e);
        }
    }
}
