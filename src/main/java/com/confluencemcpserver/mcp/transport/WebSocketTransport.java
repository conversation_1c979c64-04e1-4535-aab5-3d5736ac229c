package com.confluencemcpserver.mcp.transport;

import com.confluencemcpserver.mcp.McpServer;
import com.confluencemcpserver.mcp.protocol.JsonRpcMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * WebSocket传输层
 */
@Component
public class WebSocketTransport implements WebSocketHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketTransport.class);
    
    private final McpServer mcpServer;
    private final ObjectMapper objectMapper;
    private final ConcurrentMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();

    @Autowired
    public WebSocketTransport(McpServer mcpServer) {
        this.mcpServer = mcpServer;
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        sessions.put(sessionId, session);
        logger.info("WebSocket连接建立: {}", sessionId);
        
        // 发送欢迎消息
        sendWelcomeMessage(session);
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        if (message instanceof TextMessage) {
            String payload = ((TextMessage) message).getPayload();
            logger.debug("收到消息: {}", payload);
            
            try {
                JsonRpcMessage request = objectMapper.readValue(payload, JsonRpcMessage.class);
                
                mcpServer.handleMessage(request)
                        .subscribe(
                                response -> sendResponse(session, response),
                                error -> {
                                    logger.error("处理消息时发生错误", error);
                                    sendErrorResponse(session, request.getId(), -32603, "内部错误: " + error.getMessage());
                                }
                        );
                        
            } catch (Exception e) {
                logger.error("解析消息时发生错误", e);
                sendErrorResponse(session, null, -32700, "解析错误: " + e.getMessage());
            }
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        logger.error("WebSocket传输错误: {}", session.getId(), exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        sessions.remove(sessionId);
        logger.info("WebSocket连接关闭: {} 状态: {}", sessionId, closeStatus);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 发送欢迎消息
     */
    private void sendWelcomeMessage(WebSocketSession session) {
        try {
            JsonRpcMessage welcome = new JsonRpcMessage();
            welcome.setMethod("notifications/initialized");
            welcome.setParams(java.util.Map.of(
                    "message", "欢迎连接到Confluence MCP服务器",
                    "version", "1.0.0",
                    "timestamp", java.time.Instant.now().toString()
            ));
            
            String json = objectMapper.writeValueAsString(welcome);
            session.sendMessage(new TextMessage(json));
            
        } catch (Exception e) {
            logger.error("发送欢迎消息失败", e);
        }
    }

    /**
     * 发送响应
     */
    private void sendResponse(WebSocketSession session, JsonRpcMessage response) {
        try {
            String json = objectMapper.writeValueAsString(response);
            session.sendMessage(new TextMessage(json));
            logger.debug("发送响应: {}", json);
            
        } catch (Exception e) {
            logger.error("发送响应失败", e);
        }
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(WebSocketSession session, Object id, int code, String message) {
        try {
            JsonRpcMessage.JsonRpcError error = new JsonRpcMessage.JsonRpcError(code, message);
            JsonRpcMessage response = new JsonRpcMessage(id, error);
            
            String json = objectMapper.writeValueAsString(response);
            session.sendMessage(new TextMessage(json));
            
        } catch (Exception e) {
            logger.error("发送错误响应失败", e);
        }
    }

    /**
     * 广播通知给所有连接的客户端
     */
    public void broadcastNotification(String method, Object params) {
        JsonRpcMessage notification = new JsonRpcMessage();
        notification.setMethod(method);
        notification.setParams(params);
        
        try {
            String json = objectMapper.writeValueAsString(notification);
            
            sessions.values().forEach(session -> {
                try {
                    if (session.isOpen()) {
                        session.sendMessage(new TextMessage(json));
                    }
                } catch (IOException e) {
                    logger.error("广播通知失败: {}", session.getId(), e);
                }
            });
            
        } catch (Exception e) {
            logger.error("序列化通知失败", e);
        }
    }

    /**
     * 获取活跃会话数量
     */
    public int getActiveSessionCount() {
        return sessions.size();
    }
}
