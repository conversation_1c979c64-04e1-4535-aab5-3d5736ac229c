package com.confluencemcpserver.mcp;

import com.confluencemcpserver.config.McpConfig;
import com.confluencemcpserver.mcp.handlers.PromptHandler;
import com.confluencemcpserver.mcp.handlers.ResourceHandler;
import com.confluencemcpserver.mcp.handlers.ToolHandler;
import com.confluencemcpserver.mcp.protocol.JsonRpcMessage;
import com.confluencemcpserver.mcp.protocol.McpCapabilities;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * MCP服务器主类
 */
@Component
public class McpServer {
    
    private static final Logger logger = LoggerFactory.getLogger(McpServer.class);
    
    private final McpConfig config;
    private final ResourceHandler resourceHandler;
    private final ToolHandler toolHandler;
    private final PromptHandler promptHandler;
    private final ObjectMapper objectMapper;

    @Autowired
    public McpServer(McpConfig config, ResourceHandler resourceHandler, 
                     ToolHandler toolHandler, PromptHandler promptHandler) {
        this.config = config;
        this.resourceHandler = resourceHandler;
        this.toolHandler = toolHandler;
        this.promptHandler = promptHandler;
        this.objectMapper = new ObjectMapper();
        
        logger.info("MCP服务器初始化完成");
    }

    /**
     * 处理JSON-RPC消息
     */
    public Mono<JsonRpcMessage> handleMessage(JsonRpcMessage request) {
        logger.debug("处理消息: {}", request.getMethod());
        
        try {
            switch (request.getMethod()) {
                case "initialize":
                    return handleInitialize(request);
                case "resources/list":
                    return handleResourcesList(request);
                case "resources/read":
                    return handleResourcesRead(request);
                case "tools/list":
                    return handleToolsList(request);
                case "tools/call":
                    return handleToolsCall(request);
                case "prompts/list":
                    return handlePromptsList(request);
                case "prompts/get":
                    return handlePromptsGet(request);
                default:
                    return Mono.just(createErrorResponse(request.getId(), -32601, "方法未找到: " + request.getMethod()));
            }
        } catch (Exception e) {
            logger.error("处理消息时发生错误", e);
            return Mono.just(createErrorResponse(request.getId(), -32603, "内部错误: " + e.getMessage()));
        }
    }

    /**
     * 处理初始化请求
     */
    private Mono<JsonRpcMessage> handleInitialize(JsonRpcMessage request) {
        Map<String, Object> result = new HashMap<>();
        result.put("protocolVersion", "2025-06-18");
        result.put("serverInfo", Map.of(
                "name", config.getServerName(),
                "version", config.getServerVersion()
        ));
        
        // 设置服务器能力
        McpCapabilities capabilities = new McpCapabilities(
                config.isResourcesEnabled(),
                config.isToolsEnabled(),
                config.isPromptsEnabled(),
                config.isResourceSubscriptionEnabled(),
                config.isResourceListChangedEnabled()
        );
        result.put("capabilities", capabilities);
        
        logger.info("MCP服务器初始化完成，协议版本: 2025-06-18");
        return Mono.just(new JsonRpcMessage(request.getId(), result));
    }

    /**
     * 处理资源列表请求
     */
    private Mono<JsonRpcMessage> handleResourcesList(JsonRpcMessage request) {
        Map<String, Object> params = getParams(request);
        String cursor = (String) params.get("cursor");
        
        return resourceHandler.listResources(cursor)
                .map(resources -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("resources", resources);
                    return new JsonRpcMessage(request.getId(), result);
                })
                .onErrorReturn(createErrorResponse(request.getId(), -32603, "获取资源列表失败"));
    }

    /**
     * 处理资源读取请求
     */
    private Mono<JsonRpcMessage> handleResourcesRead(JsonRpcMessage request) {
        Map<String, Object> params = getParams(request);
        String uri = (String) params.get("uri");
        
        if (uri == null) {
            return Mono.just(createErrorResponse(request.getId(), -32602, "缺少必需参数: uri"));
        }
        
        return resourceHandler.readResource(uri)
                .map(content -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("contents", java.util.List.of(content));
                    return new JsonRpcMessage(request.getId(), result);
                })
                .onErrorReturn(createErrorResponse(request.getId(), -32002, "资源未找到"));
    }

    /**
     * 处理工具列表请求
     */
    private Mono<JsonRpcMessage> handleToolsList(JsonRpcMessage request) {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("tools", toolHandler.listTools());
            return Mono.just(new JsonRpcMessage(request.getId(), result));
        } catch (Exception e) {
            return Mono.just(createErrorResponse(request.getId(), -32603, "获取工具列表失败: " + e.getMessage()));
        }
    }

    /**
     * 处理工具调用请求
     */
    private Mono<JsonRpcMessage> handleToolsCall(JsonRpcMessage request) {
        Map<String, Object> params = getParams(request);
        String name = (String) params.get("name");
        @SuppressWarnings("unchecked")
        Map<String, Object> arguments = (Map<String, Object>) params.getOrDefault("arguments", new HashMap<>());
        
        if (name == null) {
            return Mono.just(createErrorResponse(request.getId(), -32602, "缺少必需参数: name"));
        }
        
        return toolHandler.callTool(name, arguments)
                .map(result -> new JsonRpcMessage(request.getId(), result))
                .onErrorReturn(createErrorResponse(request.getId(), -32603, "工具调用失败"));
    }

    /**
     * 处理提示列表请求
     */
    private Mono<JsonRpcMessage> handlePromptsList(JsonRpcMessage request) {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("prompts", promptHandler.listPrompts());
            return Mono.just(new JsonRpcMessage(request.getId(), result));
        } catch (Exception e) {
            return Mono.just(createErrorResponse(request.getId(), -32603, "获取提示列表失败: " + e.getMessage()));
        }
    }

    /**
     * 处理提示获取请求
     */
    private Mono<JsonRpcMessage> handlePromptsGet(JsonRpcMessage request) {
        Map<String, Object> params = getParams(request);
        String name = (String) params.get("name");
        @SuppressWarnings("unchecked")
        Map<String, Object> arguments = (Map<String, Object>) params.getOrDefault("arguments", new HashMap<>());
        
        if (name == null) {
            return Mono.just(createErrorResponse(request.getId(), -32602, "缺少必需参数: name"));
        }
        
        return promptHandler.getPrompt(name, arguments)
                .map(result -> new JsonRpcMessage(request.getId(), result))
                .onErrorReturn(createErrorResponse(request.getId(), -32603, "获取提示失败"));
    }

    /**
     * 获取请求参数
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> getParams(JsonRpcMessage request) {
        if (request.getParams() instanceof Map) {
            return (Map<String, Object>) request.getParams();
        }
        return new HashMap<>();
    }

    /**
     * 创建错误响应
     */
    private JsonRpcMessage createErrorResponse(Object id, int code, String message) {
        JsonRpcMessage.JsonRpcError error = new JsonRpcMessage.JsonRpcError(code, message);
        return new JsonRpcMessage(id, error);
    }
}
