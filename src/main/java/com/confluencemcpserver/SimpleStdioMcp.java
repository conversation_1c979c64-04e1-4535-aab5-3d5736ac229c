package com.confluencemcpserver;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.util.*;

/**
 * 简化的STDIO MCP服务器，用于测试
 */
public class SimpleStdioMcp {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final PrintWriter writer = new PrintWriter(System.out, true);
    
    public static void main(String[] args) {
        // 将日志输出到stderr，避免干扰STDIO通信
        System.err.println("简化MCP服务器启动...");
        
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(System.in))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.trim().isEmpty()) {
                    continue;
                }
                
                try {
                    System.err.println("收到消息: " + line);
                    
                    @SuppressWarnings("unchecked")
                    Map<String, Object> request = objectMapper.readValue(line, Map.class);
                    
                    String method = (String) request.get("method");
                    Object id = request.get("id");
                    
                    Map<String, Object> response = new HashMap<>();
                    response.put("jsonrpc", "2.0");
                    response.put("id", id);
                    
                    switch (method) {
                        case "initialize":
                            response.put("result", createInitializeResult());
                            break;
                        case "tools/list":
                            response.put("result", createToolsListResult());
                            break;
                        case "tools/call":
                            @SuppressWarnings("unchecked")
                            Map<String, Object> params = (Map<String, Object>) request.get("params");
                            response.put("result", createToolCallResult(params));
                            break;
                        default:
                            Map<String, Object> error = new HashMap<>();
                            error.put("code", -32601);
                            error.put("message", "Method not found: " + method);
                            response.put("error", error);
                    }
                    
                    String responseJson = objectMapper.writeValueAsString(response);
                    writer.println(responseJson);
                    writer.flush();
                    
                    System.err.println("发送响应: " + responseJson);
                    
                } catch (Exception e) {
                    System.err.println("处理消息错误: " + e.getMessage());
                    e.printStackTrace(System.err);
                    
                    // 发送错误响应
                    try {
                        Map<String, Object> errorResponse = new HashMap<>();
                        errorResponse.put("jsonrpc", "2.0");
                        errorResponse.put("id", null);
                        
                        Map<String, Object> error = new HashMap<>();
                        error.put("code", -32700);
                        error.put("message", "Parse error: " + e.getMessage());
                        errorResponse.put("error", error);
                        
                        String errorJson = objectMapper.writeValueAsString(errorResponse);
                        writer.println(errorJson);
                        writer.flush();
                    } catch (Exception ex) {
                        System.err.println("发送错误响应失败: " + ex.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("STDIO读取错误: " + e.getMessage());
            e.printStackTrace(System.err);
        }
    }
    
    private static Map<String, Object> createInitializeResult() {
        Map<String, Object> result = new HashMap<>();
        result.put("protocolVersion", "2025-06-18");
        
        Map<String, Object> serverInfo = new HashMap<>();
        serverInfo.put("name", "Simple Confluence MCP Server");
        serverInfo.put("version", "1.0.0");
        result.put("serverInfo", serverInfo);
        
        Map<String, Object> capabilities = new HashMap<>();
        capabilities.put("tools", Map.of("listChanged", false));
        result.put("capabilities", capabilities);
        
        return result;
    }
    
    private static Map<String, Object> createToolsListResult() {
        List<Map<String, Object>> tools = new ArrayList<>();
        
        // 测试连接工具
        Map<String, Object> testTool = new HashMap<>();
        testTool.put("name", "test_connection");
        testTool.put("description", "测试MCP服务器连接");
        testTool.put("inputSchema", Map.of(
            "type", "object",
            "properties", Map.of()
        ));
        tools.add(testTool);
        
        // 搜索页面工具
        Map<String, Object> searchTool = new HashMap<>();
        searchTool.put("name", "search_pages");
        searchTool.put("description", "搜索Confluence页面");
        searchTool.put("inputSchema", Map.of(
            "type", "object",
            "properties", Map.of(
                "query", Map.of("type", "string", "description", "搜索关键词"),
                "limit", Map.of("type", "integer", "description", "结果数量限制", "default", 10)
            ),
            "required", List.of("query")
        ));
        tools.add(searchTool);
        
        // 列出空间工具
        Map<String, Object> spacesTool = new HashMap<>();
        spacesTool.put("name", "list_spaces");
        spacesTool.put("description", "列出所有Confluence空间");
        spacesTool.put("inputSchema", Map.of(
            "type", "object",
            "properties", Map.of(
                "limit", Map.of("type", "integer", "description", "结果数量限制", "default", 20)
            )
        ));
        tools.add(spacesTool);
        
        Map<String, Object> result = new HashMap<>();
        result.put("tools", tools);
        return result;
    }
    
    private static Map<String, Object> createToolCallResult(Map<String, Object> params) {
        String toolName = (String) params.get("name");
        @SuppressWarnings("unchecked")
        Map<String, Object> arguments = (Map<String, Object>) params.getOrDefault("arguments", new HashMap<>());
        
        String resultText;
        switch (toolName) {
            case "test_connection":
                resultText = "🎉 MCP服务器连接成功！\n\n" +
                           "✅ 简化版Java MCP服务器运行正常\n" +
                           "✅ STDIO通信工作正常\n" +
                           "✅ 工具调用功能正常\n\n" +
                           "可用工具:\n" +
                           "- test_connection: 测试连接\n" +
                           "- search_pages: 搜索页面\n" +
                           "- list_spaces: 列出空间";
                break;
            case "search_pages":
                String query = (String) arguments.getOrDefault("query", "默认查询");
                Integer limit = (Integer) arguments.getOrDefault("limit", 10);
                resultText = String.format("🔍 搜索结果 (查询: \"%s\", 限制: %d)\n\n", query, limit) +
                           "📄 API文档 - 系统接口说明\n" +
                           "   ID: 123001\n" +
                           "   空间: DEV\n\n" +
                           "📄 用户指南 - 操作手册\n" +
                           "   ID: 123002\n" +
                           "   空间: DOC\n\n" +
                           "📄 部署指南 - 环境配置\n" +
                           "   ID: 123003\n" +
                           "   空间: OPS\n\n" +
                           "注意: 这是模拟数据，用于测试MCP连接。";
                break;
            case "list_spaces":
                Integer spaceLimit = (Integer) arguments.getOrDefault("limit", 20);
                resultText = String.format("📁 空间列表 (限制: %d)\n\n", spaceLimit) +
                           "📁 DEV - 开发团队空间\n" +
                           "   键: DEV\n" +
                           "   类型: global\n\n" +
                           "📁 DOC - 文档中心\n" +
                           "   键: DOC\n" +
                           "   类型: global\n\n" +
                           "📁 OPS - 运维空间\n" +
                           "   键: OPS\n" +
                           "   类型: global\n\n" +
                           "注意: 这是模拟数据，用于测试MCP连接。";
                break;
            default:
                resultText = "❌ 未知工具: " + toolName;
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("content", List.of(Map.of(
            "type", "text",
            "text", resultText
        )));
        result.put("isError", false);
        
        return result;
    }
}
