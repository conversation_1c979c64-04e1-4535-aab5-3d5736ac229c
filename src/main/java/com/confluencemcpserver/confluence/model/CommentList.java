package com.confluencemcpserver.confluence.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Confluence评论列表模型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CommentList {
    
    @JsonProperty("results")
    private List<Comment> results;
    
    @JsonProperty("start")
    private int start;
    
    @JsonProperty("limit")
    private int limit;
    
    @JsonProperty("size")
    private int size;
    
    @JsonProperty("_links")
    private PageList.Links links;

    // Getters and Setters
    public List<Comment> getResults() {
        return results;
    }

    public void setResults(List<Comment> results) {
        this.results = results;
    }

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public PageList.Links getLinks() {
        return links;
    }

    public void setLinks(PageList.Links links) {
        this.links = links;
    }
}
