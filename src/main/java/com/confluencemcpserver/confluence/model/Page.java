package com.confluencemcpserver.confluence.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Confluence页面模型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Page {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("type")
    private String type;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("space")
    private Space space;
    
    @JsonProperty("body")
    private Body body;
    
    @JsonProperty("version")
    private Version version;
    
    @JsonProperty("history")
    private History history;
    
    @JsonProperty("ancestors")
    private List<Page> ancestors;
    
    @JsonProperty("children")
    private Children children;
    
    @JsonProperty("_links")
    private Links links;
    
    @JsonProperty("_expandable")
    private Map<String, String> expandable;

    // Get<PERSON> and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Space getSpace() {
        return space;
    }

    public void setSpace(Space space) {
        this.space = space;
    }

    public Body getBody() {
        return body;
    }

    public void setBody(Body body) {
        this.body = body;
    }

    public Version getVersion() {
        return version;
    }

    public void setVersion(Version version) {
        this.version = version;
    }

    public History getHistory() {
        return history;
    }

    public void setHistory(History history) {
        this.history = history;
    }

    public List<Page> getAncestors() {
        return ancestors;
    }

    public void setAncestors(List<Page> ancestors) {
        this.ancestors = ancestors;
    }

    public Children getChildren() {
        return children;
    }

    public void setChildren(Children children) {
        this.children = children;
    }

    public Links getLinks() {
        return links;
    }

    public void setLinks(Links links) {
        this.links = links;
    }

    public Map<String, String> getExpandable() {
        return expandable;
    }

    public void setExpandable(Map<String, String> expandable) {
        this.expandable = expandable;
    }

    /**
     * 页面内容体
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Body {
        @JsonProperty("storage")
        private Content storage;
        
        @JsonProperty("view")
        private Content view;
        
        @JsonProperty("export_view")
        private Content exportView;

        public Content getStorage() {
            return storage;
        }

        public void setStorage(Content storage) {
            this.storage = storage;
        }

        public Content getView() {
            return view;
        }

        public void setView(Content view) {
            this.view = view;
        }

        public Content getExportView() {
            return exportView;
        }

        public void setExportView(Content exportView) {
            this.exportView = exportView;
        }
    }

    /**
     * 内容格式
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Content {
        @JsonProperty("value")
        private String value;
        
        @JsonProperty("representation")
        private String representation;

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getRepresentation() {
            return representation;
        }

        public void setRepresentation(String representation) {
            this.representation = representation;
        }
    }

    /**
     * 版本信息
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Version {
        @JsonProperty("number")
        private int number;
        
        @JsonProperty("when")
        private String when;
        
        @JsonProperty("by")
        private User by;
        
        @JsonProperty("message")
        private String message;

        public int getNumber() {
            return number;
        }

        public void setNumber(int number) {
            this.number = number;
        }

        public String getWhen() {
            return when;
        }

        public void setWhen(String when) {
            this.when = when;
        }

        public User getBy() {
            return by;
        }

        public void setBy(User by) {
            this.by = by;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }

    /**
     * 历史信息
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class History {
        @JsonProperty("latest")
        private boolean latest;
        
        @JsonProperty("createdBy")
        private User createdBy;
        
        @JsonProperty("createdDate")
        private String createdDate;

        public boolean isLatest() {
            return latest;
        }

        public void setLatest(boolean latest) {
            this.latest = latest;
        }

        public User getCreatedBy() {
            return createdBy;
        }

        public void setCreatedBy(User createdBy) {
            this.createdBy = createdBy;
        }

        public String getCreatedDate() {
            return createdDate;
        }

        public void setCreatedDate(String createdDate) {
            this.createdDate = createdDate;
        }
    }

    /**
     * 子页面信息
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Children {
        @JsonProperty("page")
        private PageList page;
        
        @JsonProperty("attachment")
        private AttachmentList attachment;
        
        @JsonProperty("comment")
        private CommentList comment;

        public PageList getPage() {
            return page;
        }

        public void setPage(PageList page) {
            this.page = page;
        }

        public AttachmentList getAttachment() {
            return attachment;
        }

        public void setAttachment(AttachmentList attachment) {
            this.attachment = attachment;
        }

        public CommentList getComment() {
            return comment;
        }

        public void setComment(CommentList comment) {
            this.comment = comment;
        }
    }

    /**
     * 链接信息
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Links {
        @JsonProperty("webui")
        private String webui;
        
        @JsonProperty("edit")
        private String edit;
        
        @JsonProperty("tinyui")
        private String tinyui;
        
        @JsonProperty("self")
        private String self;

        public String getWebui() {
            return webui;
        }

        public void setWebui(String webui) {
            this.webui = webui;
        }

        public String getEdit() {
            return edit;
        }

        public void setEdit(String edit) {
            this.edit = edit;
        }

        public String getTinyui() {
            return tinyui;
        }

        public void setTinyui(String tinyui) {
            this.tinyui = tinyui;
        }

        public String getSelf() {
            return self;
        }

        public void setSelf(String self) {
            this.self = self;
        }
    }
}
