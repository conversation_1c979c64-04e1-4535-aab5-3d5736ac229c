package com.confluencemcpserver.confluence.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * Confluence页面列表模型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PageList {
    
    @JsonProperty("results")
    private List<Page> results;
    
    @JsonProperty("start")
    private int start;
    
    @JsonProperty("limit")
    private int limit;
    
    @JsonProperty("size")
    private int size;
    
    @JsonProperty("_links")
    private Links links;

    // Getters and Setters
    public List<Page> getResults() {
        return results;
    }

    public void setResults(List<Page> results) {
        this.results = results;
    }

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public Links getLinks() {
        return links;
    }

    public void setLinks(Links links) {
        this.links = links;
    }

    /**
     * 链接信息
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Links {
        @JsonProperty("self")
        private String self;
        
        @JsonProperty("next")
        private String next;
        
        @JsonProperty("prev")
        private String prev;
        
        @JsonProperty("base")
        private String base;
        
        @JsonProperty("context")
        private String context;

        public String getSelf() {
            return self;
        }

        public void setSelf(String self) {
            this.self = self;
        }

        public String getNext() {
            return next;
        }

        public void setNext(String next) {
            this.next = next;
        }

        public String getPrev() {
            return prev;
        }

        public void setPrev(String prev) {
            this.prev = prev;
        }

        public String getBase() {
            return base;
        }

        public void setBase(String base) {
            this.base = base;
        }

        public String getContext() {
            return context;
        }

        public void setContext(String context) {
            this.context = context;
        }
    }
}
