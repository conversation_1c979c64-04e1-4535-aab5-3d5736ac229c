package com.confluencemcpserver.confluence.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * Confluence空间模型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Space {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("key")
    private String key;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("type")
    private String type;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("description")
    private Description description;
    
    @JsonProperty("homepage")
    private Page homepage;
    
    @JsonProperty("creator")
    private User creator;
    
    @JsonProperty("creationDate")
    private String creationDate;
    
    @JsonProperty("lastModifier")
    private User lastModifier;
    
    @JsonProperty("lastModificationDate")
    private String lastModificationDate;
    
    @JsonProperty("_links")
    private Links links;
    
    @JsonProperty("_expandable")
    private Map<String, String> expandable;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Description getDescription() {
        return description;
    }

    public void setDescription(Description description) {
        this.description = description;
    }

    public Page getHomepage() {
        return homepage;
    }

    public void setHomepage(Page homepage) {
        this.homepage = homepage;
    }

    public User getCreator() {
        return creator;
    }

    public void setCreator(User creator) {
        this.creator = creator;
    }

    public String getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(String creationDate) {
        this.creationDate = creationDate;
    }

    public User getLastModifier() {
        return lastModifier;
    }

    public void setLastModifier(User lastModifier) {
        this.lastModifier = lastModifier;
    }

    public String getLastModificationDate() {
        return lastModificationDate;
    }

    public void setLastModificationDate(String lastModificationDate) {
        this.lastModificationDate = lastModificationDate;
    }

    public Links getLinks() {
        return links;
    }

    public void setLinks(Links links) {
        this.links = links;
    }

    public Map<String, String> getExpandable() {
        return expandable;
    }

    public void setExpandable(Map<String, String> expandable) {
        this.expandable = expandable;
    }

    /**
     * 空间描述
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Description {
        @JsonProperty("plain")
        private Content plain;
        
        @JsonProperty("view")
        private Content view;

        public Content getPlain() {
            return plain;
        }

        public void setPlain(Content plain) {
            this.plain = plain;
        }

        public Content getView() {
            return view;
        }

        public void setView(Content view) {
            this.view = view;
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Content {
            @JsonProperty("value")
            private String value;
            
            @JsonProperty("representation")
            private String representation;

            public String getValue() {
                return value;
            }

            public void setValue(String value) {
                this.value = value;
            }

            public String getRepresentation() {
                return representation;
            }

            public void setRepresentation(String representation) {
                this.representation = representation;
            }
        }
    }

    /**
     * 链接信息
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Links {
        @JsonProperty("webui")
        private String webui;
        
        @JsonProperty("self")
        private String self;

        public String getWebui() {
            return webui;
        }

        public void setWebui(String webui) {
            this.webui = webui;
        }

        public String getSelf() {
            return self;
        }

        public void setSelf(String self) {
            this.self = self;
        }
    }
}
