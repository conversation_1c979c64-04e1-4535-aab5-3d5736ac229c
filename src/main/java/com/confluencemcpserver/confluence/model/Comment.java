package com.confluencemcpserver.confluence.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * Confluence评论模型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Comment {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("type")
    private String type;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("body")
    private Page.Body body;
    
    @JsonProperty("extensions")
    private Extensions extensions;
    
    @JsonProperty("_links")
    private Links links;
    
    @JsonProperty("_expandable")
    private Map<String, String> expandable;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Page.Body getBody() {
        return body;
    }

    public void setBody(Page.Body body) {
        this.body = body;
    }

    public Extensions getExtensions() {
        return extensions;
    }

    public void setExtensions(Extensions extensions) {
        this.extensions = extensions;
    }

    public Links getLinks() {
        return links;
    }

    public void setLinks(Links links) {
        this.links = links;
    }

    public Map<String, String> getExpandable() {
        return expandable;
    }

    public void setExpandable(Map<String, String> expandable) {
        this.expandable = expandable;
    }

    /**
     * 评论扩展信息
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Extensions {
        @JsonProperty("location")
        private String location;

        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }
    }

    /**
     * 链接信息
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Links {
        @JsonProperty("webui")
        private String webui;
        
        @JsonProperty("self")
        private String self;

        public String getWebui() {
            return webui;
        }

        public void setWebui(String webui) {
            this.webui = webui;
        }

        public String getSelf() {
            return self;
        }

        public void setSelf(String self) {
            this.self = self;
        }
    }
}
