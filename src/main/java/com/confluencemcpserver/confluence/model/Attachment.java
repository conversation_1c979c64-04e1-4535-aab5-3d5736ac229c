package com.confluencemcpserver.confluence.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * Confluence附件模型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Attachment {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("type")
    private String type;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("metadata")
    private Metadata metadata;
    
    @JsonProperty("extensions")
    private Extensions extensions;
    
    @JsonProperty("_links")
    private Links links;
    
    @JsonProperty("_expandable")
    private Map<String, String> expandable;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Metadata getMetadata() {
        return metadata;
    }

    public void setMetadata(Metadata metadata) {
        this.metadata = metadata;
    }

    public Extensions getExtensions() {
        return extensions;
    }

    public void setExtensions(Extensions extensions) {
        this.extensions = extensions;
    }

    public Links getLinks() {
        return links;
    }

    public void setLinks(Links links) {
        this.links = links;
    }

    public Map<String, String> getExpandable() {
        return expandable;
    }

    public void setExpandable(Map<String, String> expandable) {
        this.expandable = expandable;
    }

    /**
     * 附件元数据
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Metadata {
        @JsonProperty("mediaType")
        private String mediaType;
        
        @JsonProperty("fileSize")
        private long fileSize;
        
        @JsonProperty("comment")
        private String comment;

        public String getMediaType() {
            return mediaType;
        }

        public void setMediaType(String mediaType) {
            this.mediaType = mediaType;
        }

        public long getFileSize() {
            return fileSize;
        }

        public void setFileSize(long fileSize) {
            this.fileSize = fileSize;
        }

        public String getComment() {
            return comment;
        }

        public void setComment(String comment) {
            this.comment = comment;
        }
    }

    /**
     * 附件扩展信息
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Extensions {
        @JsonProperty("mediaType")
        private String mediaType;
        
        @JsonProperty("fileSize")
        private long fileSize;
        
        @JsonProperty("comment")
        private String comment;

        public String getMediaType() {
            return mediaType;
        }

        public void setMediaType(String mediaType) {
            this.mediaType = mediaType;
        }

        public long getFileSize() {
            return fileSize;
        }

        public void setFileSize(long fileSize) {
            this.fileSize = fileSize;
        }

        public String getComment() {
            return comment;
        }

        public void setComment(String comment) {
            this.comment = comment;
        }
    }

    /**
     * 链接信息
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Links {
        @JsonProperty("webui")
        private String webui;
        
        @JsonProperty("download")
        private String download;
        
        @JsonProperty("self")
        private String self;

        public String getWebui() {
            return webui;
        }

        public void setWebui(String webui) {
            this.webui = webui;
        }

        public String getDownload() {
            return download;
        }

        public void setDownload(String download) {
            this.download = download;
        }

        public String getSelf() {
            return self;
        }

        public void setSelf(String self) {
            this.self = self;
        }
    }
}
