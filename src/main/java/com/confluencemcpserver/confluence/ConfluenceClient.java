package com.confluencemcpserver.confluence;

import com.confluencemcpserver.config.ConfluenceConfig;
import com.confluencemcpserver.confluence.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * Confluence REST API客户端
 */
@Component
public class ConfluenceClient {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfluenceClient.class);
    
    private final WebClient webClient;
    private final ConfluenceConfig config;

    @Autowired
    public ConfluenceClient(ConfluenceConfig config) {
        this.config = config;
        
        String credentials = config.getUsername() + ":" + config.getPassword();
        String encodedCredentials = Base64.getEncoder()
                .encodeToString(credentials.getBytes(StandardCharsets.UTF_8));
        
        this.webClient = WebClient.builder()
                .baseUrl(config.getBaseUrl() + "/rest/api")
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Basic " + encodedCredentials)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .build();
        
        logger.info("Confluence客户端初始化完成，服务器地址: {}", config.getBaseUrl());
    }

    /**
     * 获取页面内容
     */
    public Mono<Page> getPage(String pageId, String... expand) {
        String expandParam = expand.length > 0 ? String.join(",", expand) : "body.storage,body.view,space,version,history";
        
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/content/{id}")
                        .queryParam("expand", expandParam)
                        .build(pageId))
                .retrieve()
                .bodyToMono(Page.class)
                .timeout(Duration.ofSeconds(config.getReadTimeout() / 1000))
                .doOnSuccess(page -> logger.debug("获取页面成功: {}", page.getTitle()))
                .doOnError(error -> logger.error("获取页面失败: {}", error.getMessage()));
    }

    /**
     * 搜索页面
     */
    public Mono<PageList> searchPages(String query, String spaceKey, int start, int limit) {
        return webClient.get()
                .uri(uriBuilder -> {
                    var builder = uriBuilder
                            .path("/content")
                            .queryParam("start", start)
                            .queryParam("limit", limit)
                            .queryParam("expand", "body.view,space,version");
                    
                    if (query != null && !query.trim().isEmpty()) {
                        builder.queryParam("title", query);
                    }
                    
                    if (spaceKey != null && !spaceKey.trim().isEmpty()) {
                        builder.queryParam("spaceKey", spaceKey);
                    }
                    
                    return builder.build();
                })
                .retrieve()
                .bodyToMono(PageList.class)
                .timeout(Duration.ofSeconds(config.getReadTimeout() / 1000))
                .doOnSuccess(result -> logger.debug("搜索页面成功，找到 {} 个结果", result.getSize()))
                .doOnError(error -> logger.error("搜索页面失败: {}", error.getMessage()));
    }

    /**
     * 获取页面附件
     */
    public Mono<AttachmentList> getPageAttachments(String pageId, int start, int limit) {
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/content/{id}/child/attachment")
                        .queryParam("start", start)
                        .queryParam("limit", limit)
                        .queryParam("expand", "metadata")
                        .build(pageId))
                .retrieve()
                .bodyToMono(AttachmentList.class)
                .timeout(Duration.ofSeconds(config.getReadTimeout() / 1000))
                .doOnSuccess(result -> logger.debug("获取页面附件成功，找到 {} 个附件", result.getSize()))
                .doOnError(error -> logger.error("获取页面附件失败: {}", error.getMessage()));
    }

    /**
     * 获取页面评论
     */
    public Mono<CommentList> getPageComments(String pageId, int start, int limit) {
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/content/{id}/child/comment")
                        .queryParam("start", start)
                        .queryParam("limit", limit)
                        .queryParam("expand", "body.view")
                        .build(pageId))
                .retrieve()
                .bodyToMono(CommentList.class)
                .timeout(Duration.ofSeconds(config.getReadTimeout() / 1000))
                .doOnSuccess(result -> logger.debug("获取页面评论成功，找到 {} 个评论", result.getSize()))
                .doOnError(error -> logger.error("获取页面评论失败: {}", error.getMessage()));
    }

    /**
     * 获取所有空间
     */
    public Mono<List<Space>> getAllSpaces(int start, int limit) {
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/space")
                        .queryParam("start", start)
                        .queryParam("limit", limit)
                        .queryParam("expand", "description.plain,homepage")
                        .build())
                .retrieve()
                .bodyToMono(SpaceList.class)
                .map(SpaceList::getResults)
                .timeout(Duration.ofSeconds(config.getReadTimeout() / 1000))
                .doOnSuccess(spaces -> logger.debug("获取空间列表成功，找到 {} 个空间", spaces.size()))
                .doOnError(error -> logger.error("获取空间列表失败: {}", error.getMessage()));
    }

    /**
     * 获取空间信息
     */
    public Mono<Space> getSpace(String spaceKey) {
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/space/{key}")
                        .queryParam("expand", "description.plain,homepage")
                        .build(spaceKey))
                .retrieve()
                .bodyToMono(Space.class)
                .timeout(Duration.ofSeconds(config.getReadTimeout() / 1000))
                .doOnSuccess(space -> logger.debug("获取空间信息成功: {}", space.getName()))
                .doOnError(error -> logger.error("获取空间信息失败: {}", error.getMessage()));
    }

    /**
     * 空间列表包装类
     */
    public static class SpaceList {
        private List<Space> results;
        private int start;
        private int limit;
        private int size;

        public List<Space> getResults() {
            return results;
        }

        public void setResults(List<Space> results) {
            this.results = results;
        }

        public int getStart() {
            return start;
        }

        public void setStart(int start) {
            this.start = start;
        }

        public int getLimit() {
            return limit;
        }

        public void setLimit(int limit) {
            this.limit = limit;
        }

        public int getSize() {
            return size;
        }

        public void setSize(int size) {
            this.size = size;
        }
    }
}
