@echo off
echo ========================================
echo 测试 Confluence MCP Server (Upgraded)
echo ========================================
echo.

REM 设置测试环境变量
set CONFLUENCE_BASE_URL=https://test-confluence.atlassian.net
set CONFLUENCE_USERNAME=<EMAIL>
set CONFLUENCE_API_TOKEN=test-token
set SPRING_PROFILES_ACTIVE=test

echo 测试配置:
echo - Confluence URL: %CONFLUENCE_BASE_URL%
echo - Username: %CONFLUENCE_USERNAME%
echo - Profile: %SPRING_PROFILES_ACTIVE%
echo.

echo 启动测试模式...
echo 发送测试请求到MCP服务器...
echo.

REM 创建测试输入
echo {"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2025-06-18","clientInfo":{"name":"Test Client","version":"1.0.0"}}} > test_input.json
echo {"jsonrpc":"2.0","id":2,"method":"tools/list","params":{}} >> test_input.json
echo {"jsonrpc":"2.0","id":3,"method":"tools/call","params":{"name":"test_connection","arguments":{}}} >> test_input.json

echo 测试输入已准备完成
echo 使用以下命令运行测试:
echo.
echo java -jar target\confluenceMcpServer-1.0.0-UPGRADED.jar ^< test_input.json
echo.

pause
