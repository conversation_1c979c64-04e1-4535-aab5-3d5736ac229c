const WebSocket = require('ws');

let messageId = 1;

function testMCPServer() {
    console.log('🔗 连接到MCP服务器...');
    
    const ws = new WebSocket('ws://localhost:8080/mcp');
    
    ws.on('open', function() {
        console.log('✅ WebSocket连接成功');
        
        // 1. 发送初始化消息
        console.log('\n📤 发送初始化消息...');
        const initMessage = {
            jsonrpc: "2.0",
            id: messageId++,
            method: "initialize",
            params: {
                protocolVersion: "2025-06-18",
                clientInfo: {
                    name: "MCP Tools Test",
                    version: "1.0.0"
                }
            }
        };
        ws.send(JSON.stringify(initMessage));
    });
    
    ws.on('message', function(data) {
        try {
            const message = JSON.parse(data.toString());
            console.log('\n📨 收到消息:');
            console.log(JSON.stringify(message, null, 2));
            
            // 如果是初始化响应，请求工具列表
            if (message.id === 1 && message.result) {
                console.log('\n📤 请求工具列表...');
                const toolsMessage = {
                    jsonrpc: "2.0",
                    id: messageId++,
                    method: "tools/list",
                    params: {}
                };
                ws.send(JSON.stringify(toolsMessage));
            }
            
            // 如果是工具列表响应，测试一个工具
            if (message.id === 2 && message.result && message.result.tools) {
                console.log('\n✅ 工具列表获取成功！');
                console.log(`找到 ${message.result.tools.length} 个工具:`);
                message.result.tools.forEach(tool => {
                    console.log(`  - ${tool.name}: ${tool.description}`);
                });
                
                // 测试list_spaces工具
                console.log('\n📤 测试list_spaces工具...');
                const testToolMessage = {
                    jsonrpc: "2.0",
                    id: messageId++,
                    method: "tools/call",
                    params: {
                        name: "list_spaces",
                        arguments: {
                            limit: 5
                        }
                    }
                };
                ws.send(JSON.stringify(testToolMessage));
            }
            
            // 如果是工具调用响应
            if (message.id === 3) {
                if (message.result) {
                    console.log('\n✅ 工具调用成功！');
                } else if (message.error) {
                    console.log('\n❌ 工具调用失败:');
                    console.log(message.error);
                }
                
                // 测试完成，关闭连接
                setTimeout(() => {
                    console.log('\n🏁 测试完成，关闭连接');
                    ws.close();
                }, 1000);
            }
            
        } catch (e) {
            console.log('❌ 解析消息失败:', e.message);
            console.log('原始消息:', data.toString());
        }
    });
    
    ws.on('close', function() {
        console.log('🔌 WebSocket连接已关闭');
    });
    
    ws.on('error', function(error) {
        console.log('❌ WebSocket错误:', error.message);
    });
}

// 检查是否有ws模块
try {
    require('ws');
    testMCPServer();
} catch (e) {
    console.log('❌ 缺少ws模块，请先安装: npm install ws');
    console.log('或者使用浏览器测试: 打开 test-mcp-connection.html');
}
