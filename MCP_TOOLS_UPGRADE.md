# MCP工具列表逻辑升级文档

## 概述

根据MCP官方文档规范（2025-06-18版本），对Confluence MCP服务器的工具列表逻辑进行了全面升级，以符合最新的协议要求。

## 主要改进

### 1. 分页支持

#### 修改前
- `tools/list` 请求不支持分页
- 一次性返回所有工具

#### 修改后
- 支持 `cursor` 参数进行分页
- 响应包含 `nextCursor` 字段
- 默认页面大小为10个工具

```java
// 新的分页API
public Map<String, Object> listTools(String cursor)
```

### 2. 输出模式支持

#### 修改前
- 工具定义只包含输入模式
- 缺少结构化输出验证

#### 修改后
- 每个工具都定义了详细的输出模式
- 支持结构化结果验证
- 符合JSON Schema规范

```java
Map<String, Object> outputSchema = Map.of(
    "type", "object",
    "properties", Map.of(
        "content", Map.of(
            "type", "array",
            "items", Map.of(
                "type", "object",
                "properties", Map.of(
                    "type", Map.of("type", "string"),
                    "text", Map.of("type", "string")
                )
            )
        ),
        "isError", Map.of("type", "boolean")
    ),
    "required", List.of("content", "isError")
);
```

### 3. 工具注解

#### 修改前
- 工具定义缺少元数据
- 无法指定工具的优先级和分类

#### 修改后
- 每个工具都包含详细注解
- 支持受众、优先级、分类等元数据

```java
Map<String, Object> annotations = Map.of(
    "audience", List.of("user", "assistant"),
    "priority", 0.8,
    "category", "search"
);
```

### 4. 工具列表变更通知

#### 修改前
- 不支持工具列表变更通知
- 客户端无法感知工具变化

#### 修改后
- 支持 `notifications/tools/list_changed` 通知
- 可配置是否启用通知功能
- 提供通知发送机制

## 修改的文件

### 1. McpConfig.java
- 添加 `toolsListChangedEnabled` 配置项
- 支持工具列表变更通知的开关

### 2. McpCapabilities.java
- 更新构造函数支持工具列表变更能力声明
- 正确传递 `listChanged` 参数

### 3. McpServer.java
- 更新 `handleToolsList` 方法支持分页
- 添加 `createToolsListChangedNotification` 方法
- 处理 `cursor` 参数和 `nextCursor` 响应

### 4. ToolHandler.java
- 重构工具列表逻辑，支持分页
- 为所有工具添加输出模式定义
- 为所有工具添加注解支持
- 添加工具列表变更通知方法

### 5. McpTool.java
- 已支持 `outputSchema` 和 `annotations` 字段
- 无需修改，现有结构已满足要求

## 工具优先级说明

根据工具的重要性和使用频率，设置了不同的优先级：

- **test_connection**: 1.0 (最高优先级，测试工具)
- **get_page_content**: 0.9 (高优先级，核心功能)
- **search_pages**: 0.8 (高优先级，搜索功能)
- **list_spaces**: 0.7 (中等优先级，导航功能)
- **list_attachments**: 0.6 (中等优先级，附件管理)
- **get_page_links**: 0.5 (中等优先级，链接提取)
- **get_comments**: 0.4 (较低优先级，评论功能)

## 工具分类

为便于管理和使用，将工具按功能分类：

- **testing**: 测试相关工具
- **content**: 内容获取工具
- **search**: 搜索相关工具
- **spaces**: 空间管理工具
- **attachments**: 附件管理工具
- **links**: 链接相关工具
- **comments**: 评论相关工具

## API变更

### tools/list 请求

#### 请求格式
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/list",
  "params": {
    "cursor": "optional-cursor-value"
  }
}
```

#### 响应格式
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "tools": [
      {
        "name": "search_pages",
        "title": "🔍 搜索页面",
        "description": "在Confluence中搜索页面",
        "inputSchema": { ... },
        "outputSchema": { ... },
        "annotations": { ... }
      }
    ],
    "nextCursor": "next-page-cursor"
  }
}
```

### 工具列表变更通知

```json
{
  "jsonrpc": "2.0",
  "method": "notifications/tools/list_changed"
}
```

## 配置选项

在 `application.yml` 中添加：

```yaml
mcp:
  tools-list-changed-enabled: true
```

## 向后兼容性

- 保留了原有的 `listTools()` 方法，确保向后兼容
- 现有客户端可以继续使用，无需修改
- 新功能通过可选参数提供

## 测试

创建了完整的单元测试 `ToolHandlerTest.java`，覆盖：

- 分页功能测试
- 工具模式验证
- 注解结构验证
- 输出模式验证
- 通知功能测试

## 符合规范

本次升级完全符合MCP官方文档规范：

1. ✅ 支持分页的 `tools/list` 请求
2. ✅ 正确的 `nextCursor` 响应
3. ✅ 工具列表变更通知
4. ✅ 输出模式定义
5. ✅ 工具注解支持
6. ✅ 正确的能力声明

## 使用示例

```java
// 获取第一页工具
Map<String, Object> result = toolHandler.listTools(null);
List<McpTool> tools = (List<McpTool>) result.get("tools");

// 获取下一页工具
String nextCursor = (String) result.get("nextCursor");
if (nextCursor != null) {
    Map<String, Object> nextPage = toolHandler.listTools(nextCursor);
}

// 发送工具列表变更通知
toolHandler.notifyToolsListChanged();
```
