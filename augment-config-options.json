{"description": "Confluence MCP Server 配置选项", "options": [{"name": "选项1: 使用简化版本（推荐）", "config": {"mcpServers": {"confluence": {"command": "C:\\Users\\<USER>\\Desktop\\idea\\confluenceMcpServer\\simple-mcp.bat", "env": {"CONFLUENCE_BASE_URL": "http://localhost:8080/confluence", "CONFLUENCE_USERNAME": "admin", "CONFLUENCE_PASSWORD": "admin"}}}}}, {"name": "选项2: 直接使用Java命令", "config": {"mcpServers": {"confluence": {"command": "java", "args": ["-cp", "C:\\Users\\<USER>\\Desktop\\idea\\confluenceMcpServer\\target\\classes;C:\\Users\\<USER>\\Desktop\\idea\\confluenceMcpServer\\target\\dependency\\*", "com.confluencemcpserver.SimpleStdioMcp"], "cwd": "C:\\Users\\<USER>\\Desktop\\idea\\confluenceMcpServer", "env": {"CONFLUENCE_BASE_URL": "http://localhost:8080/confluence", "CONFLUENCE_USERNAME": "admin", "CONFLUENCE_PASSWORD": "admin"}}}}}, {"name": "选项3: 使用JAR包", "config": {"mcpServers": {"confluence": {"command": "java", "args": ["-jar", "C:\\Users\\<USER>\\Desktop\\idea\\confluenceMcpServer\\target\\confluenceMcpServer-0.0.1-SNAPSHOT.jar"], "env": {"CONFLUENCE_BASE_URL": "http://localhost:8080/confluence", "CONFLUENCE_USERNAME": "admin", "CONFLUENCE_PASSWORD": "admin"}}}}}]}