2025-07-29 18:14:47 [com.confluencemcpserver.McpStdioApplication.main()] INFO  c.c.McpStdioApplication - Starting McpStdioApplication using Java 17.0.1 with PID 25752 (C:\Users\<USER>\Desktop\idea\confluenceMcpServer\target\classes started by wangjp in C:\Users\<USER>\Desktop\idea\confluenceMcpServer)
2025-07-29 18:14:47 [com.confluencemcpserver.McpStdioApplication.main()] DEBUG c.c.McpStdioApplication - Running with Spring Boot v3.5.4, Spring v6.2.9
2025-07-29 18:14:47 [com.confluencemcpserver.McpStdioApplication.main()] INFO  c.c.McpStdioApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-29 18:14:48 [com.confluencemcpserver.McpStdioApplication.main()] INFO  c.c.confluence.ConfluenceClient - Confluence�ͻ��˳�ʼ����ɣ���������ַ: http://localhost:8080/confluence
2025-07-29 18:14:48 [com.confluencemcpserver.McpStdioApplication.main()] INFO  c.confluencemcpserver.mcp.McpServer - MCP��������ʼ�����
2025-07-29 18:14:48 [com.confluencemcpserver.McpStdioApplication.main()] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/mcp] in 'webSocketHandlerMapping'
2025-07-29 18:14:48 [com.confluencemcpserver.McpStdioApplication.main()] INFO  c.c.McpStdioApplication - Started McpStdioApplication in 2.124 seconds (process running for 7.511)
2025-07-29 18:14:48 [com.confluencemcpserver.McpStdioApplication.main()] INFO  c.c.McpStdioApplication - MCP��������׼���������ȴ�STDIO����...
2025-07-29 18:14:48 [com.confluencemcpserver.McpStdioApplication.main()] INFO  c.c.mcp.transport.StdioTransport - ����MCP STDIO�����...
2025-07-29 18:17:40 [com.confluencemcpserver.McpStdioApplication.main()] INFO  c.c.McpStdioApplication - Starting McpStdioApplication using Java 17.0.1 with PID 26408 (C:\Users\<USER>\Desktop\idea\confluenceMcpServer\target\classes started by wangjp in C:\Users\<USER>\Desktop\idea\confluenceMcpServer)
2025-07-29 18:17:40 [com.confluencemcpserver.McpStdioApplication.main()] DEBUG c.c.McpStdioApplication - Running with Spring Boot v3.5.4, Spring v6.2.9
2025-07-29 18:17:40 [com.confluencemcpserver.McpStdioApplication.main()] INFO  c.c.McpStdioApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-29 18:17:41 [com.confluencemcpserver.McpStdioApplication.main()] INFO  c.c.confluence.ConfluenceClient - Confluence�ͻ��˳�ʼ����ɣ���������ַ: http://localhost:8080/confluence
2025-07-29 18:17:41 [com.confluencemcpserver.McpStdioApplication.main()] INFO  c.confluencemcpserver.mcp.McpServer - MCP��������ʼ�����
2025-07-29 18:17:41 [com.confluencemcpserver.McpStdioApplication.main()] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/mcp] in 'webSocketHandlerMapping'
2025-07-29 18:17:42 [com.confluencemcpserver.McpStdioApplication.main()] INFO  c.c.McpStdioApplication - Started McpStdioApplication in 2.254 seconds (process running for 6.85)
2025-07-29 18:17:42 [com.confluencemcpserver.McpStdioApplication.main()] INFO  c.c.McpStdioApplication - MCP��������׼���������ȴ�STDIO����...
2025-07-29 18:17:42 [com.confluencemcpserver.McpStdioApplication.main()] INFO  c.c.mcp.transport.StdioTransport - ����MCP STDIO�����...
