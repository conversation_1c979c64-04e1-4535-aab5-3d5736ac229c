{"mcpServers": {"confluence-mcp-upgraded": {"command": "java", "args": ["-jar", "C:/Users/<USER>/Desktop/idea/confluenceMcpServer/target/confluenceMcpServer-1.0.0-UPGRADED.jar"], "env": {"CONFLUENCE_BASE_URL": "https://your-confluence-domain.atlassian.net", "CONFLUENCE_USERNAME": "<EMAIL>", "CONFLUENCE_API_TOKEN": "your-api-token", "SPRING_PROFILES_ACTIVE": "production", "MCP_TOOLS_LIST_CHANGED_ENABLED": "true", "MCP_RESOURCES_ENABLED": "true", "MCP_PROMPTS_ENABLED": "true"}}}}