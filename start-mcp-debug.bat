@echo off
setlocal enabledelayedexpansion

REM 设置工作目录
cd /d "C:\Users\<USER>\Desktop\idea\confluenceMcpServer"

REM 检查JAR文件是否存在
if not exist "target\confluenceMcpServer-0.0.1-SNAPSHOT.jar" (
    echo JAR文件不存在，正在编译...
    call mvnw.cmd package -DskipTests -q
    if !errorlevel! neq 0 (
        echo 编译失败
        exit /b 1
    )
)

REM 启动MCP服务器，重定向错误到stderr
java -jar target\confluenceMcpServer-0.0.1-SNAPSHOT.jar 2>&1
